{"__meta": {"id": "Xdf9273c943e106895a07e0e65deea6a5", "datetime": "2025-07-29 17:34:44", "utime": **********.928242, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[17:34:44] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.923387, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753810483.88353, "end": **********.928287, "duration": 1.0447571277618408, "duration_str": "1.04s", "measures": [{"label": "Booting", "start": 1753810483.88353, "relative_start": 0, "end": **********.74201, "relative_end": **********.74201, "duration": 0.8584802150726318, "duration_str": "858ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.742043, "relative_start": 0.8585131168365479, "end": **********.928293, "relative_end": 5.9604644775390625e-06, "duration": 0.1862499713897705, "duration_str": "186ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48334232, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.02374, "accumulated_duration_str": "23.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8038201, "duration": 0.01472, "duration_str": "14.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 62.005}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.836424, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 62.005, "width_percent": 2.696}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.840036, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "radhe_same", "start_percent": 64.701, "width_percent": 3.033}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8450558, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 67.734, "width_percent": 3.243}, {"sql": "select * from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.849725, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 70.977, "width_percent": 3.454}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.866739, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 74.431, "width_percent": 5.139}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.876817, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 79.57, "width_percent": 3.623}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.882386, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 83.193, "width_percent": 3.496}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.888122, "duration": 0.00316, "duration_str": "3.16ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 86.689, "width_percent": 13.311}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 542, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 545, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-545896748 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-545896748\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1882026256 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1882026256\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-613247077 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613247077\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2086157718 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2718 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; omx_ai_suite_session=eyJpdiI6Im02aWF1ZnE3RTQvSmtiMW5BcERQV3c9PSIsInZhbHVlIjoiUGE4L1gwR29Ya0tIc2pGYVovTzZId2k2TGFSdnRLV21sK1AyMlNIZVRoOVdjaU1jN0Robk5HZ2doSkpmY0FXemFhUzdMQjUxNmZCY2xtcDF0ODBvbVpNQmJtL05WWGFTQnRrcDF0R09NUG0yV2x0VlhQMk1TSXpWU1lWY1FFSEhuNHlidlN4V1BQVkh2ajNIb3VYZk1LNjRzOXFXc1Y4MU1jQkh3Y0xwYWU3NkxXMmFYZENJSldkbFJzTWJKSE5pQ25oMGIrMGVMNFdjMG45ZEI5b1Z4NjR0M0EvWjBVSHZyVW12TFgvZ2ZuOEVEdlR1Wi9DRUxvWnp2cXlTZk1LWDJqVmcwZExoZThzM0YzNWVUdmV0Z2tmQmc1UFdrMHpkVDhQdWhIMmxVN3J4MGtOOUZqRm1QcG1yK2Z6STcreUcxcEdnLzNDaTNVakcxdlJiVWdGQXlLV3BKejdidXBPM3ZNcU9EQkZWaG9mTkl1Z2Jjb0JwK2wvOUJRYjNYN2ZjYlJCU3ZUSVRhc29CMFdCR3FRY09wQkdML25lYXp6bzFDTG5EWS81TnowdUxVMVpLUUJsUEhxNUJkZDJGZVFWVENkOC9ETytPaHBiSzE2RUdkRkdTTFc0SGZURzlCVjBteEZrSDJPWUtVVmNTQ1RoRGg1TDMvdW8yYkNuNHhPZGsiLCJtYWMiOiIzZDU3ZjdlZDM2MDg5ZDJhMWRkMDMxYjk2ZDhjNGY1YTY3MmE3YmNlYzRjYmM3YmQyZTBkMThhZGFlZDEwYjIwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjU0S0V0bGNoVGlIcnBxelNHWklqeGc9PSIsInZhbHVlIjoiYUtkS3lmdEVvSUNoYUNvZWRjUitKUGV6UVhnUlZ2YnBmVFB6b3padW1VV040Nzg2OUM1MXV1WVYxUWhHOHNONWg0ak1HK0NaWW5hNytqa29sekQyZS90TjY1TWNXeDh1djNxZjhBRk1hWDBCOEoxKzA1aWFBQm12eThhckdXbEtDSDJIWXQxcWtVbnYyK0xMWG53bFowUmNTUUgvNDM0SEUyOHU0c1J4eVZ6SzVNVlJBVk5mNE9EZFI1cTV1bSs1czlLdWIwZFRBSG5ZeXNmQnp2WHRvWnY2OVZYcXdWeUZzZ1RsMWUwaFZra1BGWlBSYkdwOU4zdStOUmdiM0RtNjVUNTB5M2JhQy9sQkJHcjhjNWxheHhWajhEWkk3eHVFQ2FIUjJsZCs3MHl2WlJ2cHVtSEJxbXQ4Z1lxak9JdG9WbjJYaUhGM0RuMHIyQzlTaUFjellKZXBVbytGK1FNd0xaK2p5ZFFObWpQOEp5ZEZZQnRTd2J6c0Q3bk1DNGdXM1dNUE9zNkRZdFFxWi80YzFaMTdJQ1JOQlNTRThTVzdMTW8zVFFGVVpnTXEwRVY4d1M4RFpiZHc2SUNCbEhIRGFJdW5ERjFFbCt3cjk0U3BRVE9kR1Q4b3RaZnpPOGRFMzRKK0VhN0NiWUhRZTNoOWVxQ3pzU3l2MzR0ZytZNEkiLCJtYWMiOiIxYzMzNzA3YTMzNTA5YzE5ZGQzN2M0ZDQ4MjExN2ZiMTQ4Yzk0MTc0NjM4M2NjYmU0ZTEwYjcyZTIyYTgwNGJiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ii84M0E3eUIvaGhqdU9sUXlLTkI0Rmc9PSIsInZhbHVlIjoiU3oxdVlJV1VQTnNGazRxcm5pZ1luMkhzRFFYQTc3aGFtY1FOT0taWm9oRWZwL3VvRDB3STR2SjIzeEhJRWRCdFNyOFpKbmYwUldNaTlhQWd3VExnR1N5bTBpV2d5SGJueWszK1YzMFJLdVgxWFl4eE5GZUVKb1dlQVdvSHNzeUdGRUtqM0ZyeWhVanF5eGd5TmpoeDJhT3dQT0xTSUU0TUxYcXg5dHNCWFgrMEJQclg2NEhNbjZsUlJYZ2xEOGxBdzZDL21pOENOYklkNjNNb1NOUFF2T1dVdExidTI1Z09pZnM0c0QraGl6YUZ0aGlRckRtcTlIWTAwUGc4SHlhdDZveWRTWVBnZ2IycjRqeEZXN1BUZXVXWVp6dXhwRU54eTJRVk4ycDE0NXZDVTVVMHJVdnFiZXhHcXFTaVo5WFJsd0g2Ym9ON0paU1doOHpkamRPTVlMOENmQXlUaHJab1hxeVZscm5BczdtbnFUOXJHUk8rQ3VVdENMMTFpNFp2UjdwUCtBdkFPY1pZQ1NmejI0cUN6REtwV1V5a1UvTll2MlBYUTZGQVUyL01Qc2dZT3JZNURHN2lzd0lmKzdWU2pPZ2FBU0FPWThralMweEpCS1hhSlp5M1FLNVVuYlNzak82bVQwSVZ0enlRbHJkUGhKSDVhV1pGVUNvSGtzYkciLCJtYWMiOiIxODg4ZmJjZjRlZmIyZTQ1MzZjMGI3MDlkNDAxMDZmZTBhMWVmYjZkZDkyN2UxOTBjYzk1YjBlNzQyNTk4MmEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086157718\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1141185863 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WnT8J6dr1q3cGe2zznYjRzgdZFNO79ma6VzwnyJd</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MYGtST6gzPoxGtZLL5j826OymweTDicqK5wxV4qK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141185863\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1735349873 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:34:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdNYUpKeVdVQmsrSFFsRGNTUHRlM0E9PSIsInZhbHVlIjoiL1hEd3RVL0ZORWVyZ2s2cWswK3hjeUZITGhyODhPV1RaMTM5blBnRThZVlVpTURVYjJBTzloZzZBTTRyRDlIQXA2K3YzSWw1SkVJcjNMTnNkMFJIZUllV2xZZms5QjFQMHlVNGVWMHUveHJHYVkxZjFua2FVUU9IUFhPQXpUbW1IOExMK0xqSldodGlyZXRKUWw0ZXBpU3I1a0JWNitteXJoSTBlTVluNlRYYXFNZEt0TG5JNHE5VWZJOG1sNms3N29WbG1SOXk2WklyVFNDMnZqRzlOeGhzeHJwNE9JZHh1b2pRS3ZXT1JMZHg1Y2kwU081ZlA4c080V1gvM2hKUGdYNEIzWHlURGwxd0wxdW5naW0vRGRjTk5Zak43RVYxUjc3RXN5b29sVnpKM2hsaEt6YlkxbndKQkRvcEVILytZNUZnMzFmMUNobXJyYWVlMjdUWXRrT0llY3NGUXRneCs4ODB3RDFNY2w1cDdIaHpsMkdZeGJKZDl1VlJLSmNxYTFDcEhlaFhCcmdYRm5OZXpMYlA1ZWZ6Vkd4L25GdDFvYUo4T05JeDh4NGtGeTgrZExudi84MnNzY0JQZ1crM2lVckhrY1ZHMjh0dUpZeEZpMlA2S3dqajYvZEc4d1BpQ20vM1hGRmNIOEdyeE56MVFiMlkyRmE3QnV0ME9FQ1giLCJtYWMiOiJjZTg0NWNlYWUwNGM5ZTBjYzE3ODIxZjJhMTMwNjM0ODU1NWM2ODFiZGZlOTZiYzRjMjNiMjVhOGVjMzZkMWJhIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:34:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNsZ2RNOGVoMWxKVGlXSmZSMk14ZlE9PSIsInZhbHVlIjoicWJzYmN5b05xVzA5Wm9ndWM3cHV6VTBGSVlkayswazV1dU01d0FBTjNrSm5HWTFQS2FnSmFJYUdMVVpnMWZjM3JXQkhURWdCblJJTXhUUmhOOXJwNGFLTVF3NzVBVlpQckI1ZjFDVHlGRnJsWlpRMCs0elY2QWROc1p4bFVyNXQ0YThmUWlweUtxU1VFNFZ2UDBvVzBzYlVFU0pFTE91d20rbGF4Vk5jUFFPYjhOSWNIK2h6RFV2TWxsWGZGamZiNHk5Q0JDSTJ3eVNaRDRIaG1tQVJQVmdsY0FnU0JhWU5tRGhqL2QxaFp0TUxWdml2bWVaTFVMU1dUZWVvZ0xJQytZaEpadzRScUNHc3pMRWttSWFDemQ1Smdib2J4SEZsNDlvWExVM1BZV2dTUE1uQXNsTFlqU3RNbWV3cnA5dGNTSFl5RXIwS0dXR2pVQXBLa3Vjdk51R3ZQR0x2SFFnSnI4dVhwdWF2UVpZL1JrWmFlTTFYY2hRVUxsSWw0Y0NLTGJ0TG8rMFdkb2hYVFFacWVZblRYcTVSV0g3a0tzQW9wOTlPVlR4aXB4ZTlSNm1IbitDVFNyenhUbWV1c282WmZ2WVlwdjZPZDh3dTVBTDBrNDEwY0xHL0o3NWpxeVo3TFVsZUxYMEk5bGlKR3ZTaGVna3VUbkkzQlJyd2sxYnUiLCJtYWMiOiIwYTE5MWY2NjkzYWZlZmQ1MmFiNWFkMmQ5NDU1OWMyZmQwZWE2MWNkZjQ2MzFlOTE2ZWNlMDJlZWM2MGI3NWU1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:34:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdNYUpKeVdVQmsrSFFsRGNTUHRlM0E9PSIsInZhbHVlIjoiL1hEd3RVL0ZORWVyZ2s2cWswK3hjeUZITGhyODhPV1RaMTM5blBnRThZVlVpTURVYjJBTzloZzZBTTRyRDlIQXA2K3YzSWw1SkVJcjNMTnNkMFJIZUllV2xZZms5QjFQMHlVNGVWMHUveHJHYVkxZjFua2FVUU9IUFhPQXpUbW1IOExMK0xqSldodGlyZXRKUWw0ZXBpU3I1a0JWNitteXJoSTBlTVluNlRYYXFNZEt0TG5JNHE5VWZJOG1sNms3N29WbG1SOXk2WklyVFNDMnZqRzlOeGhzeHJwNE9JZHh1b2pRS3ZXT1JMZHg1Y2kwU081ZlA4c080V1gvM2hKUGdYNEIzWHlURGwxd0wxdW5naW0vRGRjTk5Zak43RVYxUjc3RXN5b29sVnpKM2hsaEt6YlkxbndKQkRvcEVILytZNUZnMzFmMUNobXJyYWVlMjdUWXRrT0llY3NGUXRneCs4ODB3RDFNY2w1cDdIaHpsMkdZeGJKZDl1VlJLSmNxYTFDcEhlaFhCcmdYRm5OZXpMYlA1ZWZ6Vkd4L25GdDFvYUo4T05JeDh4NGtGeTgrZExudi84MnNzY0JQZ1crM2lVckhrY1ZHMjh0dUpZeEZpMlA2S3dqajYvZEc4d1BpQ20vM1hGRmNIOEdyeE56MVFiMlkyRmE3QnV0ME9FQ1giLCJtYWMiOiJjZTg0NWNlYWUwNGM5ZTBjYzE3ODIxZjJhMTMwNjM0ODU1NWM2ODFiZGZlOTZiYzRjMjNiMjVhOGVjMzZkMWJhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:34:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNsZ2RNOGVoMWxKVGlXSmZSMk14ZlE9PSIsInZhbHVlIjoicWJzYmN5b05xVzA5Wm9ndWM3cHV6VTBGSVlkayswazV1dU01d0FBTjNrSm5HWTFQS2FnSmFJYUdMVVpnMWZjM3JXQkhURWdCblJJTXhUUmhOOXJwNGFLTVF3NzVBVlpQckI1ZjFDVHlGRnJsWlpRMCs0elY2QWROc1p4bFVyNXQ0YThmUWlweUtxU1VFNFZ2UDBvVzBzYlVFU0pFTE91d20rbGF4Vk5jUFFPYjhOSWNIK2h6RFV2TWxsWGZGamZiNHk5Q0JDSTJ3eVNaRDRIaG1tQVJQVmdsY0FnU0JhWU5tRGhqL2QxaFp0TUxWdml2bWVaTFVMU1dUZWVvZ0xJQytZaEpadzRScUNHc3pMRWttSWFDemQ1Smdib2J4SEZsNDlvWExVM1BZV2dTUE1uQXNsTFlqU3RNbWV3cnA5dGNTSFl5RXIwS0dXR2pVQXBLa3Vjdk51R3ZQR0x2SFFnSnI4dVhwdWF2UVpZL1JrWmFlTTFYY2hRVUxsSWw0Y0NLTGJ0TG8rMFdkb2hYVFFacWVZblRYcTVSV0g3a0tzQW9wOTlPVlR4aXB4ZTlSNm1IbitDVFNyenhUbWV1c282WmZ2WVlwdjZPZDh3dTVBTDBrNDEwY0xHL0o3NWpxeVo3TFVsZUxYMEk5bGlKR3ZTaGVna3VUbkkzQlJyd2sxYnUiLCJtYWMiOiIwYTE5MWY2NjkzYWZlZmQ1MmFiNWFkMmQ5NDU1OWMyZmQwZWE2MWNkZjQ2MzFlOTE2ZWNlMDJlZWM2MGI3NWU1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:34:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1735349873\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-427313549 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-427313549\", {\"maxDepth\":0})</script>\n"}}
{"__meta": {"id": "X4a62d8962228e4af06bd0a48b41df217", "datetime": "2025-07-29 17:14:59", "utime": **********.884592, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753809298.823104, "end": **********.884622, "duration": 1.0615181922912598, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1753809298.823104, "relative_start": 0, "end": **********.711465, "relative_end": **********.711465, "duration": 0.8883609771728516, "duration_str": "888ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.7115, "relative_start": 0.8883960247039795, "end": **********.884625, "relative_end": 2.86102294921875e-06, "duration": 0.1731250286102295, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46595248, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.808932, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.827266, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.865611, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.0151, "accumulated_duration_str": "15.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.768657, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 18.411}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.77498, "duration": 0.0073, "duration_str": "7.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 18.411, "width_percent": 48.344}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7882268, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 66.755, "width_percent": 3.974}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.809986, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 70.728, "width_percent": 5.563}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.828678, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 76.291, "width_percent": 6.093}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.84762, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 82.384, "width_percent": 9.139}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8557541, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 91.523, "width_percent": 4.503}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.86003, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 96.026, "width_percent": 3.974}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8iOnsm9Zaxv8EDiN41lrOnBzF5vlTO5UUFH0Q6U5", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-549273334 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-549273334\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1100241877 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1100241877\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-281297635 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-281297635\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-808973262 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808973262\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1555465444 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1555465444\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-960283133 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:14:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFXZy9PM1E0UjdhSUhMVlAvRDRjSUE9PSIsInZhbHVlIjoiUHFLWGNTOEo3QlA4RXZKNDN1ODZWOGlFOVJkNm5mVlV0VXc0OWo0Qzl6a29OTDYrZ3RrRjNtVEdTNXA5Y3pyUkpQNkppejVIK3VPVWVTdjhRUnI0bnN3c1V1aTYxMUJ0eVZETFNieENtYWZhNDdtT1ZuM0hXNUhEN05iUit1Qy93WkJhSFZManZ6ejhXcUVGYVdLalV4aytxZlZZcE9oTHl0QjJZSjJlUW5BSFFnN1BjNUlCL3RBQWdjblAxV2k2dUg3WGlLUlhTVW54N0t3RkxuL1NtR05XbXpoeEVIaVVscnlnbjhyYmk4QXluMERPTVRHdUlKaktDZlpkMlhheTREckh3c21QZUUxREkzRmt2VGxQNEJ6ak83c0FlQmlseWg2enZBTyt4VzRnQzFYOUdNOHhpVHlaRzlkVkhOb09TU1FscHRjOWJjanJtbEFYeTdGMlRiWVpHTHo0b2dLQnFJdzd1RW9EbG9zOTIrbVR5TnlTWVI2cThHcG0reE9yamd6UmpGOWtFZWx3ZEUxY3R4OEFlSUtOd1B3d0NyclVSV2FJV3RCWUFya1d0U0JpajhQMXJMZGNGSFd0WEY2R0xjbHN1c1cvdU9xbm9qaFRwQjdhK1Nxa2pMRnIvR3hnQit0VlFRSXlBZnJZcWRLRVFzTG5mVVYvNFZCY3M2TGYiLCJtYWMiOiI3NmI1MGM4NjE1MmViZTVhMTk3ZGFlNjNkZGVlNzY2YTc1NmI1ZGM1YTM4ZWYxNjZhZmM1OGM2NmVlNjgzYmZlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:14:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im41ZlkxUFV6ZzVwcTF4cGIxVnlmbWc9PSIsInZhbHVlIjoiNmNsNXFHbEtTVktsNHl5aytuRGRRNHlWb2VFMlNJN3Voc0NmYVlDYllFZXBBd1lydVRwVTV6am5TMmhJa1ZJZUdHRlN4Z0N2REI2SUpyMEV0NitpSEtydnpTdU9WNkRiWVFSd0JyVUhYSTFOM2tTSGtEZnFnN2hNTWMrbkRidXBnQ3JaS0tZQytiWXc3djJqQ2FqQSs1RVc4VjB6b0JHYThkV0ZkNURFUVhoK0xBNWZWT256djM4Mmk4Y0Z2bEJvbXMza3F2dzZYVUR6YlovRkRhbGlKZ3BqTldhaGlPeHd2S0tIUUVadVd0TVRadE1qNkVxUHl4NERqUmNMSUhYeDYvQysvR1psOWRpRVAyNktvVGpYbGcrK3J6d3hIUzhZYU9KaXVyd2tNdE9yckJId1NIZzdLMXpUZzZTOVpZUUF2Wmc2YkVhQmx1ZjZkUTlyazJvbzdCaDVKaEhHcjA5Q0NjY3RLTFhHQVZqamJrMTAvc0NWSXhLREJ5ZjhZOWplNmNDbjhZcFpzSFN6NVRiU1hmdnAxdEtJcWdoRXp1Y0hLN2p2QXhiOFl4SGlkSXFBQVdCSlRrQUt1YTd1RnBjVTFwaVJOekxNZVErTUVGb0loOERZZW5sRURjN3RDQ0pldWRwY0l2OFNEUThCaXlqcUhJU051aS9nVXV2T3NnVzkiLCJtYWMiOiJlYzU5ODE2YzRmNTBmMDIwZGYwYzcyY2Q2MGFmN2IzOGUxYTE0NjI5NjRiZTc5OWUzZDIxZDI0YTc0ZGMwMjAxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:14:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFXZy9PM1E0UjdhSUhMVlAvRDRjSUE9PSIsInZhbHVlIjoiUHFLWGNTOEo3QlA4RXZKNDN1ODZWOGlFOVJkNm5mVlV0VXc0OWo0Qzl6a29OTDYrZ3RrRjNtVEdTNXA5Y3pyUkpQNkppejVIK3VPVWVTdjhRUnI0bnN3c1V1aTYxMUJ0eVZETFNieENtYWZhNDdtT1ZuM0hXNUhEN05iUit1Qy93WkJhSFZManZ6ejhXcUVGYVdLalV4aytxZlZZcE9oTHl0QjJZSjJlUW5BSFFnN1BjNUlCL3RBQWdjblAxV2k2dUg3WGlLUlhTVW54N0t3RkxuL1NtR05XbXpoeEVIaVVscnlnbjhyYmk4QXluMERPTVRHdUlKaktDZlpkMlhheTREckh3c21QZUUxREkzRmt2VGxQNEJ6ak83c0FlQmlseWg2enZBTyt4VzRnQzFYOUdNOHhpVHlaRzlkVkhOb09TU1FscHRjOWJjanJtbEFYeTdGMlRiWVpHTHo0b2dLQnFJdzd1RW9EbG9zOTIrbVR5TnlTWVI2cThHcG0reE9yamd6UmpGOWtFZWx3ZEUxY3R4OEFlSUtOd1B3d0NyclVSV2FJV3RCWUFya1d0U0JpajhQMXJMZGNGSFd0WEY2R0xjbHN1c1cvdU9xbm9qaFRwQjdhK1Nxa2pMRnIvR3hnQit0VlFRSXlBZnJZcWRLRVFzTG5mVVYvNFZCY3M2TGYiLCJtYWMiOiI3NmI1MGM4NjE1MmViZTVhMTk3ZGFlNjNkZGVlNzY2YTc1NmI1ZGM1YTM4ZWYxNjZhZmM1OGM2NmVlNjgzYmZlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:14:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im41ZlkxUFV6ZzVwcTF4cGIxVnlmbWc9PSIsInZhbHVlIjoiNmNsNXFHbEtTVktsNHl5aytuRGRRNHlWb2VFMlNJN3Voc0NmYVlDYllFZXBBd1lydVRwVTV6am5TMmhJa1ZJZUdHRlN4Z0N2REI2SUpyMEV0NitpSEtydnpTdU9WNkRiWVFSd0JyVUhYSTFOM2tTSGtEZnFnN2hNTWMrbkRidXBnQ3JaS0tZQytiWXc3djJqQ2FqQSs1RVc4VjB6b0JHYThkV0ZkNURFUVhoK0xBNWZWT256djM4Mmk4Y0Z2bEJvbXMza3F2dzZYVUR6YlovRkRhbGlKZ3BqTldhaGlPeHd2S0tIUUVadVd0TVRadE1qNkVxUHl4NERqUmNMSUhYeDYvQysvR1psOWRpRVAyNktvVGpYbGcrK3J6d3hIUzhZYU9KaXVyd2tNdE9yckJId1NIZzdLMXpUZzZTOVpZUUF2Wmc2YkVhQmx1ZjZkUTlyazJvbzdCaDVKaEhHcjA5Q0NjY3RLTFhHQVZqamJrMTAvc0NWSXhLREJ5ZjhZOWplNmNDbjhZcFpzSFN6NVRiU1hmdnAxdEtJcWdoRXp1Y0hLN2p2QXhiOFl4SGlkSXFBQVdCSlRrQUt1YTd1RnBjVTFwaVJOekxNZVErTUVGb0loOERZZW5sRURjN3RDQ0pldWRwY0l2OFNEUThCaXlqcUhJU051aS9nVXV2T3NnVzkiLCJtYWMiOiJlYzU5ODE2YzRmNTBmMDIwZGYwYzcyY2Q2MGFmN2IzOGUxYTE0NjI5NjRiZTc5OWUzZDIxZDI0YTc0ZGMwMjAxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:14:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-960283133\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1264982478 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8iOnsm9Zaxv8EDiN41lrOnBzF5vlTO5UUFH0Q6U5</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264982478\", {\"maxDepth\":0})</script>\n"}}
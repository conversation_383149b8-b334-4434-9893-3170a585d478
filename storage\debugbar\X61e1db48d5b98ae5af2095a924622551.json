{"__meta": {"id": "X61e1db48d5b98ae5af2095a924622551", "datetime": "2025-07-29 17:40:47", "utime": **********.172846, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753810846.215418, "end": **********.172889, "duration": 0.9574708938598633, "duration_str": "957ms", "measures": [{"label": "Booting", "start": 1753810846.215418, "relative_start": 0, "end": **********.041418, "relative_end": **********.041418, "duration": 0.8259999752044678, "duration_str": "826ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.041441, "relative_start": 0.****************, "end": **********.172892, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "131ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XPoMfhm2WVbdYYabY2XzNK1mxqjUMrWTx1m7WEfD", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-47497750 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-47497750\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1389860387 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1389860387\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1947498784 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1947498784\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1959311173 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1959311173\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-588977025 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-588977025\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1799178789 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:40:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklMQ3k5TlIxaDdyK3p1eXVnZG5ucFE9PSIsInZhbHVlIjoiVzlpNERSKzlXTU0rc0NrbzR5d2FXdzVxNUVqTnFmMGNLU3p6c0V6eW5JOUhkdUtiMG9ueFFzcTJUWldGM0crK1d0Y1MydmpHdVlhYVByY3plblg2VkE0cjRzdVhOVEhjU2l5YnZRbDlUdktoZkJXZWl5Z2RXbmNrWVlmZ2ZMNUhiUXNvSElGbVpLV2I2alRFOGNJOUhXRnFkQUN3U09zVi96bjNmZ2RqSVgyZlE5c1NZcmcrYkZCTHUyUkhGV3ZJTE9WZ1BOSWhYQXl6MXA1TnZNV3k2c3dZMXhiVzVST1Y4d3lYRllSUXVTU2pmRG9zWFFFdzNMUC9wNEg3QXNKUkxlZmdqNmgxajRTYmZ1SGM1QkdhZFNyRzVxSkFoNDZnVUVobXRndCtQLy9ieEROazNLM2owR0lkRzhIY3dVNnZGN2g1SXlqRkoxdWFBMzlBY3dZVklrdVlFeG9NRnZ5MU9hdmlxajU4RDhEaG0zK3RQZkJpLzNNS0pqSjVWQWkrQ2RIckhpbDA5Z2xzQXk3UE1vb3pHU29NaVRQYnlSZ2FIVzhiMm5YTk5heEpuVmVYL3BxeTlLeUhGbGVmOSs3MzJwdFAweEc0bWpoWDh1VVFNYUh0bUViQkM3S3lOcE5EOEdBZVZNLzY0bEYxZXBZRVdhRzJGd0Q5OGlnaEFWUXIiLCJtYWMiOiIzZjc1M2UyZjk4YTE5Y2Y5Mjc3NTA1YjA5NWVmN2NiZmIxMzI1NjQ5ZDkyNDA0N2NhZmY5N2YwZDNiZjM1ZWRmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:40:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjRDd0s2ejhCMlMrSmNXVGo4VVJ0NXc9PSIsInZhbHVlIjoiUVUxb1lVbFRmdTd2YTRXKzFpeFVlZ2U4aFl6N2xHL3Z3SER2L2VZQXZqdkNTSjA1YitXTG9OTmd0bFpFR2xoTnNOTk81YnI0TU9sMFNGNlNGT1plcEptSnJOcUh0Rm9rb0orK1JoSzU3TWRWeVN6b2p6ZS9ZTHU4SjRsT2dESFpaTkF4dDFjY2pLMzhTVHU5VnRUT2xIaFhNMzVzUHFZMDhxcTlxTnpFaXpqLzlWZDdhNEsrSGwrak5JbkNJT01MRmpzVnhPM1l5TVZkS2tGUnptblhyL004eE5wdVd1bXV3djBKS0wzczlxZFBnUU91WCtDZFZyTll6Y2NGdjQyekdRbzJEeTQ4em9Xd2VyaDBSeWJsbThjOGpBTmlZUjlPc1Y4dGdRdlloZTU4ODFrSFNvVTNSRUN3SWtDOWJ4ZUJxVGx5b3JENm1iaWYvNTliSW8rUVA1c1hmRVFPWnFBRXBNMWxwd2xyMVpmdDl6OHBPSWd4TEkyTE5xSDNDNndINmFkdTE1UERJOWo4aTZLRzFtcDBaQWo5NWZLTk5aVGNtS1ZYUnFrTjRIMmhzUHFQaHM5K0NJMU4wY0lZZytia3EyOWY0ejQwUUdvR0I5L3E2SXlnWVNncTNPczcxQmFoeU4zTzNtWEtiMit1MVpJODhQeWtsMFNwRFMvMHovYWYiLCJtYWMiOiJjMmEyY2VmMTNkNmMyZDg3ZTdjZTMyNjNlMTM1YTAxM2Y1NTU2N2FhNzEwZjdjM2FhYzAzNzg1M2M5M2FhMGE5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:40:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklMQ3k5TlIxaDdyK3p1eXVnZG5ucFE9PSIsInZhbHVlIjoiVzlpNERSKzlXTU0rc0NrbzR5d2FXdzVxNUVqTnFmMGNLU3p6c0V6eW5JOUhkdUtiMG9ueFFzcTJUWldGM0crK1d0Y1MydmpHdVlhYVByY3plblg2VkE0cjRzdVhOVEhjU2l5YnZRbDlUdktoZkJXZWl5Z2RXbmNrWVlmZ2ZMNUhiUXNvSElGbVpLV2I2alRFOGNJOUhXRnFkQUN3U09zVi96bjNmZ2RqSVgyZlE5c1NZcmcrYkZCTHUyUkhGV3ZJTE9WZ1BOSWhYQXl6MXA1TnZNV3k2c3dZMXhiVzVST1Y4d3lYRllSUXVTU2pmRG9zWFFFdzNMUC9wNEg3QXNKUkxlZmdqNmgxajRTYmZ1SGM1QkdhZFNyRzVxSkFoNDZnVUVobXRndCtQLy9ieEROazNLM2owR0lkRzhIY3dVNnZGN2g1SXlqRkoxdWFBMzlBY3dZVklrdVlFeG9NRnZ5MU9hdmlxajU4RDhEaG0zK3RQZkJpLzNNS0pqSjVWQWkrQ2RIckhpbDA5Z2xzQXk3UE1vb3pHU29NaVRQYnlSZ2FIVzhiMm5YTk5heEpuVmVYL3BxeTlLeUhGbGVmOSs3MzJwdFAweEc0bWpoWDh1VVFNYUh0bUViQkM3S3lOcE5EOEdBZVZNLzY0bEYxZXBZRVdhRzJGd0Q5OGlnaEFWUXIiLCJtYWMiOiIzZjc1M2UyZjk4YTE5Y2Y5Mjc3NTA1YjA5NWVmN2NiZmIxMzI1NjQ5ZDkyNDA0N2NhZmY5N2YwZDNiZjM1ZWRmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:40:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjRDd0s2ejhCMlMrSmNXVGo4VVJ0NXc9PSIsInZhbHVlIjoiUVUxb1lVbFRmdTd2YTRXKzFpeFVlZ2U4aFl6N2xHL3Z3SER2L2VZQXZqdkNTSjA1YitXTG9OTmd0bFpFR2xoTnNOTk81YnI0TU9sMFNGNlNGT1plcEptSnJOcUh0Rm9rb0orK1JoSzU3TWRWeVN6b2p6ZS9ZTHU4SjRsT2dESFpaTkF4dDFjY2pLMzhTVHU5VnRUT2xIaFhNMzVzUHFZMDhxcTlxTnpFaXpqLzlWZDdhNEsrSGwrak5JbkNJT01MRmpzVnhPM1l5TVZkS2tGUnptblhyL004eE5wdVd1bXV3djBKS0wzczlxZFBnUU91WCtDZFZyTll6Y2NGdjQyekdRbzJEeTQ4em9Xd2VyaDBSeWJsbThjOGpBTmlZUjlPc1Y4dGdRdlloZTU4ODFrSFNvVTNSRUN3SWtDOWJ4ZUJxVGx5b3JENm1iaWYvNTliSW8rUVA1c1hmRVFPWnFBRXBNMWxwd2xyMVpmdDl6OHBPSWd4TEkyTE5xSDNDNndINmFkdTE1UERJOWo4aTZLRzFtcDBaQWo5NWZLTk5aVGNtS1ZYUnFrTjRIMmhzUHFQaHM5K0NJMU4wY0lZZytia3EyOWY0ejQwUUdvR0I5L3E2SXlnWVNncTNPczcxQmFoeU4zTzNtWEtiMit1MVpJODhQeWtsMFNwRFMvMHovYWYiLCJtYWMiOiJjMmEyY2VmMTNkNmMyZDg3ZTdjZTMyNjNlMTM1YTAxM2Y1NTU2N2FhNzEwZjdjM2FhYzAzNzg1M2M5M2FhMGE5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:40:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1799178789\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XPoMfhm2WVbdYYabY2XzNK1mxqjUMrWTx1m7WEfD</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}
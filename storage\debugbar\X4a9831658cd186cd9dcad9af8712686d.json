{"__meta": {"id": "X4a9831658cd186cd9dcad9af8712686d", "datetime": "2025-07-29 17:38:15", "utime": **********.752671, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753810694.65791, "end": **********.7527, "duration": 1.094789981842041, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1753810694.65791, "relative_start": 0, "end": **********.627649, "relative_end": **********.627649, "duration": 0.9697389602661133, "duration_str": "970ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.627684, "relative_start": 0.****************, "end": **********.752704, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9lV81n3nqPR1daO2Snmer7wBC5CI4EKbmogpcQiP", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1399000011 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1399000011\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-840245450 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-840245450\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1361342324 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1361342324\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-462886803 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462886803\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-727605046 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-727605046\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-41524989 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:38:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBaNkRwSFpCYnl0NUM2d3RPNTlJZUE9PSIsInZhbHVlIjoiMEpjK1lkcGFTejNVdmFkRnNLd1B6UStvTkFVV0ZDZkdLZDJUbHVtWTRDNytlTFJVS1cyNUFMMDlxK3pIblh2ZTV5eVNQdWFPOHVRTGFNdy8xSkQzbS93L1RPdHB2ZEgvZEE2Sm83UlpxYXpWNnIyZThxRVV2aGNaK01WMXIza1MrNWtrVzFsSlRCQ0RJc1B0TThxOHVnYUxydlpETWtaRkxJNGFTbkREY09CcVk2MnNZOEVZVmQ1L1lTc2g3aldRY2tjdndXWlJIM0VkY3F2ZFhWNEJKR0p3QTFpMWtLREo0K3JreXZZbDJmT2tLUWtWWWRNM0diTHFqc1JzYWFCVmcvakZmSTM4ekdIM1k3RThhdEsvckJvYS9OaEt2ckZ4cmlzYWV0QWw3bEVaUDBtRWxWdWZxQ0VhZ2RiK281U2FhaHdlVXlHU3BwVmpyaFl4aWRJZDRwSDZmMW1rM25UcG9BMEczbENGTmpOd0x1Vi9ZRVk3R0FQV0g4M1lYSUJZeS9pNXZOOElXb293OVZjR2w2ZFk0Si9peXNhajR0WFkwOSticGYwcVNaQWZQcm5TQ1RVT29PV1dJakJEVm15MnMrVVBtVlJlSkhCREt2bXFIY1QxU1c0TjVkM29iYU9SMUpvRGU0UFpldUwxdWFKQndJSUJtYUtGUEpTWGh4eFYiLCJtYWMiOiI2ZmFkMDk4MzQ5ZGY1NmU1OTUyZTY1ZGNiZmYwODg0MDVmMTFmNGY5NTMyMTU2Y2NjZTUzMTUxMDRiOTJjMzc3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:38:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImNRN2hlSzRqNTk1Q05PSW5IclBKaFE9PSIsInZhbHVlIjoibXlwWEpzeFI4UUNoQTNPd3d2dldObnBLYSswTmlRUkhEb2grR2xRcHZuMmxFOFRQbEVwTUdoQStlbEtCcWNjSHpMaHp5Um9Wc2U0ZE9rTVhjN3oxM1lUeWM4WVJXcFFLWjJPWlZ5SUlzWmduVlY1dXBZeDZqYXZMMGhnSlRwY01nUWlyNytTQ0p5b1N0UXBTeVU1TnMxL0J2bGZlM3JKTHRpQzhVTFVIcGdYSjJBMFdHVWJ5L1BKa3NUTHNJRkY4MWJMQ3FXQ0lrWjZtV3VyNkR0ZzFUa3FWanFlZEF3cVZpWk1UbG5hOTdJc3ZtTUZrNk5QSHcxL1pkZUs2MXZoQ0k0RWNoSkJJWFlwY2lieHpFRGxCNS84NEdSaUlib3hJcTFpU2tRUDQxUEJWU2RnNy9FNlJydHFWUC9EclJDYWMzNjRxTU5EOGIxUWpXTTNSaG5ZR2FBZDJ2U3pkVzB5elpCampqTSt5L2lXM0JISnFtZDJpQWRoVnZ5MThlSnNVY3hpb0dkUWdWZjZRZkZzdFBVYWgrT0NyTzdhNWJ6L3RqYVFSMTJhY0dEWWJiUDdRbWJpTEVyTVJTdVRwd0sxRVBWZTVKdTVybmdXMnRkMTJ2RS9Sam5zV2x2bFB0Tkt6Yk10WnVpcFhaRUZmVGYrdUd2YzFzTFl0em5RNWxwaTYiLCJtYWMiOiIwMDRlMmMxOTBhNjE3ZjA5M2Q2MjYxMWU1YjA4NTc4YjBmYjIyNTU1NjhjNzI0N2Q5ZDk0NDU2N2VlYTYyYmE1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:38:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBaNkRwSFpCYnl0NUM2d3RPNTlJZUE9PSIsInZhbHVlIjoiMEpjK1lkcGFTejNVdmFkRnNLd1B6UStvTkFVV0ZDZkdLZDJUbHVtWTRDNytlTFJVS1cyNUFMMDlxK3pIblh2ZTV5eVNQdWFPOHVRTGFNdy8xSkQzbS93L1RPdHB2ZEgvZEE2Sm83UlpxYXpWNnIyZThxRVV2aGNaK01WMXIza1MrNWtrVzFsSlRCQ0RJc1B0TThxOHVnYUxydlpETWtaRkxJNGFTbkREY09CcVk2MnNZOEVZVmQ1L1lTc2g3aldRY2tjdndXWlJIM0VkY3F2ZFhWNEJKR0p3QTFpMWtLREo0K3JreXZZbDJmT2tLUWtWWWRNM0diTHFqc1JzYWFCVmcvakZmSTM4ekdIM1k3RThhdEsvckJvYS9OaEt2ckZ4cmlzYWV0QWw3bEVaUDBtRWxWdWZxQ0VhZ2RiK281U2FhaHdlVXlHU3BwVmpyaFl4aWRJZDRwSDZmMW1rM25UcG9BMEczbENGTmpOd0x1Vi9ZRVk3R0FQV0g4M1lYSUJZeS9pNXZOOElXb293OVZjR2w2ZFk0Si9peXNhajR0WFkwOSticGYwcVNaQWZQcm5TQ1RVT29PV1dJakJEVm15MnMrVVBtVlJlSkhCREt2bXFIY1QxU1c0TjVkM29iYU9SMUpvRGU0UFpldUwxdWFKQndJSUJtYUtGUEpTWGh4eFYiLCJtYWMiOiI2ZmFkMDk4MzQ5ZGY1NmU1OTUyZTY1ZGNiZmYwODg0MDVmMTFmNGY5NTMyMTU2Y2NjZTUzMTUxMDRiOTJjMzc3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:38:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImNRN2hlSzRqNTk1Q05PSW5IclBKaFE9PSIsInZhbHVlIjoibXlwWEpzeFI4UUNoQTNPd3d2dldObnBLYSswTmlRUkhEb2grR2xRcHZuMmxFOFRQbEVwTUdoQStlbEtCcWNjSHpMaHp5Um9Wc2U0ZE9rTVhjN3oxM1lUeWM4WVJXcFFLWjJPWlZ5SUlzWmduVlY1dXBZeDZqYXZMMGhnSlRwY01nUWlyNytTQ0p5b1N0UXBTeVU1TnMxL0J2bGZlM3JKTHRpQzhVTFVIcGdYSjJBMFdHVWJ5L1BKa3NUTHNJRkY4MWJMQ3FXQ0lrWjZtV3VyNkR0ZzFUa3FWanFlZEF3cVZpWk1UbG5hOTdJc3ZtTUZrNk5QSHcxL1pkZUs2MXZoQ0k0RWNoSkJJWFlwY2lieHpFRGxCNS84NEdSaUlib3hJcTFpU2tRUDQxUEJWU2RnNy9FNlJydHFWUC9EclJDYWMzNjRxTU5EOGIxUWpXTTNSaG5ZR2FBZDJ2U3pkVzB5elpCampqTSt5L2lXM0JISnFtZDJpQWRoVnZ5MThlSnNVY3hpb0dkUWdWZjZRZkZzdFBVYWgrT0NyTzdhNWJ6L3RqYVFSMTJhY0dEWWJiUDdRbWJpTEVyTVJTdVRwd0sxRVBWZTVKdTVybmdXMnRkMTJ2RS9Sam5zV2x2bFB0Tkt6Yk10WnVpcFhaRUZmVGYrdUd2YzFzTFl0em5RNWxwaTYiLCJtYWMiOiIwMDRlMmMxOTBhNjE3ZjA5M2Q2MjYxMWU1YjA4NTc4YjBmYjIyNTU1NjhjNzI0N2Q5ZDk0NDU2N2VlYTYyYmE1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:38:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41524989\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-995078522 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9lV81n3nqPR1daO2Snmer7wBC5CI4EKbmogpcQiP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995078522\", {\"maxDepth\":0})</script>\n"}}
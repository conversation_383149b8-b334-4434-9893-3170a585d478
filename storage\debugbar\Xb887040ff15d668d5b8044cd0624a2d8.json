{"__meta": {"id": "Xb887040ff15d668d5b8044cd0624a2d8", "datetime": "2025-07-29 17:13:29", "utime": **********.94878, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[17:13:29] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.944099, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753809208.894799, "end": **********.94883, "duration": 1.0540308952331543, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": 1753809208.894799, "relative_start": 0, "end": **********.706985, "relative_end": **********.706985, "duration": 0.8121860027313232, "duration_str": "812ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.707017, "relative_start": 0.8122179508209229, "end": **********.948834, "relative_end": 4.0531158447265625e-06, "duration": 0.24181699752807617, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48331472, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.04799, "accumulated_duration_str": "47.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.762201, "duration": 0.02398, "duration_str": "23.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 49.969}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.806604, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 49.969, "width_percent": 1.271}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.8104, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "radhe_same", "start_percent": 51.24, "width_percent": 1.25}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 84 or `ch_messages`.`to_id` = 84 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["84", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.816437, "duration": 0.01617, "duration_str": "16.17ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 52.49, "width_percent": 33.695}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 84", "type": "query", "params": [], "bindings": ["client", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 364}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.836619, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:364", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=364", "ajax": false, "filename": "MessagesController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 86.185, "width_percent": 1.917}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.85287, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 88.102, "width_percent": 1.313}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.862322, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 89.414, "width_percent": 1.73}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.867558, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 91.144, "width_percent": 1.896}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.873513, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 93.04, "width_percent": 6.96}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 542, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 545, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/expense/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1204469161 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1204469161\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-271672764 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-271672764\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-903337854 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903337854\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1131354092 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2718 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; omx_ai_suite_session=eyJpdiI6Im02aWF1ZnE3RTQvSmtiMW5BcERQV3c9PSIsInZhbHVlIjoiUGE4L1gwR29Ya0tIc2pGYVovTzZId2k2TGFSdnRLV21sK1AyMlNIZVRoOVdjaU1jN0Robk5HZ2doSkpmY0FXemFhUzdMQjUxNmZCY2xtcDF0ODBvbVpNQmJtL05WWGFTQnRrcDF0R09NUG0yV2x0VlhQMk1TSXpWU1lWY1FFSEhuNHlidlN4V1BQVkh2ajNIb3VYZk1LNjRzOXFXc1Y4MU1jQkh3Y0xwYWU3NkxXMmFYZENJSldkbFJzTWJKSE5pQ25oMGIrMGVMNFdjMG45ZEI5b1Z4NjR0M0EvWjBVSHZyVW12TFgvZ2ZuOEVEdlR1Wi9DRUxvWnp2cXlTZk1LWDJqVmcwZExoZThzM0YzNWVUdmV0Z2tmQmc1UFdrMHpkVDhQdWhIMmxVN3J4MGtOOUZqRm1QcG1yK2Z6STcreUcxcEdnLzNDaTNVakcxdlJiVWdGQXlLV3BKejdidXBPM3ZNcU9EQkZWaG9mTkl1Z2Jjb0JwK2wvOUJRYjNYN2ZjYlJCU3ZUSVRhc29CMFdCR3FRY09wQkdML25lYXp6bzFDTG5EWS81TnowdUxVMVpLUUJsUEhxNUJkZDJGZVFWVENkOC9ETytPaHBiSzE2RUdkRkdTTFc0SGZURzlCVjBteEZrSDJPWUtVVmNTQ1RoRGg1TDMvdW8yYkNuNHhPZGsiLCJtYWMiOiIzZDU3ZjdlZDM2MDg5ZDJhMWRkMDMxYjk2ZDhjNGY1YTY3MmE3YmNlYzRjYmM3YmQyZTBkMThhZGFlZDEwYjIwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkUyeWJVME9BS1lGS2RTUEZxNTZTVXc9PSIsInZhbHVlIjoiQ1hDUGY0TlpjTVl2RTZubkIyY0trYmtidmlQa01SOFdIdWIrWUZOS29yQWt5Y2g4cFhvbDdQSnEwN2VMRDM5Q2pOeWw1NUJabDRkZkp2WVZidWdvTlBZSmtaaVRZeEk4eUtJSUNCclhiVkdHSUpabmwyQTRFdEluMUVXenU2NEpBQjd3anhmU2U2UzJuVVBOV1VrWUR2YUdvWVd2UElocVFBNURXbVoybEVtWCtOR2xmcXh0NDdyTW9EUHpzMkI1OG8yeXRmYzR4cVdGUlRPdnV0emp2ZUNvWE5PcWx2Y2xaWUJXZk0zYktjbkRuSU5KU2pVKzlPR0NzYWpvQ2RDd1JKeklFSjF0djJ6SU9uN2VtR0dQdEpsMlprV24vQ1h3NE15aU82cjVyMERpaWRDN1lkc0kzeDdXb01GdnpwaGpiWVROdVlHN2xUQ2xaTlErZ2E2NjJ0TXZsVFMxRlNudzdOazg5OGtIUGQzRDhrY3lFd1EyTW9oV0pWMitZTnE1Q2JxZnlwdE11bHNEajZRSHJPOW1jS25iSW1xenE5SDFjVmxWcXo4TjRPaklRNkQ0ek1iZ2lUSlJUYTVFWUhtMG1NN0VlZitjZWJlYmthNkpCWmFYUHBjZ1dNUCtzMjYrZkVsUzZ2YWhxRHAvQjdKVWRnUGh2QmsrUVlzNHJta3EiLCJtYWMiOiIzMzYzMDgzZjZmNzM4MDA1MmQ0NmJiMzNjMTU1YWI5NmJlZmU4OTkzNmE2YmJlY2RiYmE5NmYwNTBhOWU0YmUzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ik1VaVVNTmlVMzBIa3h0RldxRnp2cWc9PSIsInZhbHVlIjoiR3hvWjFaRVV5SzhFU0hrKy9jZ2tXMUVzQ2NzZTlBVHE2bXJGVVlRa1VpUXR4enUyTzNQM2I2TU5jc1pkZDJWNU5RSmFBaGxCMUtxRzRteEhvR3FnbHRCVm95YUI4aTk2dzBlRVovbDUxRzBNZ0N3Und1MU4xVzdaV0J5RTJLL3JESU1ScDdSelR5c0xuaWFMdWZFYjZMaVk5MkRCWWJnbldWSDBVY0EzcjJQV0NrTEJsNWpPVEdvdWYvK0p5UXRlWDVqTHBoR05ZSkJtVXNPYTBiOUc1SW9xVkllb1FZVlhxMXhYcDBvU1dtQUNKa2hMOG5zMzRjS3FVYmlKWm1STEhvT1M5ZldJeXZSRGNtOUFvSUxoWlZCS2JOQm9xWFlKWE5ZS2VKd1FZOGo4UkRBOVhyalo1RWJvTncrM2VMemxpcFp6ZncwT21YclQ2d2RDV0R4UHhOQm42SzhsNWZNU0hiTjBpOWlUTHBnWmtlRnR2OW5EK1M3YTNUSUdQR2doQ250aW5KcWZ4MmtydEdZaXROUXhIZUJrSzZUYVhmd0dWMmlnSkxSWGdwM0tSQnJSZ21FL1Uzb0F0b0pjWkJ6VW5lYTRmMllnL2o0VlAxQU1zOFBaS243azBNazRsb1QrQTczajJxc1lzSWVrT3BqdXk0STl2cFZUV1pVb05qVVoiLCJtYWMiOiJkMWQ1MzAwMGVkN2VhMDllMGQ2YWUzYTc4MDI1OTRhY2JhYmUwYTIxNjdkMGViZjE4OWZhY2QzNjE0Y2MyY2EzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131354092\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WnT8J6dr1q3cGe2zznYjRzgdZFNO79ma6VzwnyJd</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MYGtST6gzPoxGtZLL5j826OymweTDicqK5wxV4qK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1256680527 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:13:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iml2VS9FMGVXbjlBdTA5ZzVOQ3FhTHc9PSIsInZhbHVlIjoiYW5oL2kxZGdKY0hVNzhzb1g2YnRHVXEyL2gybzdSYnJLSjRMb2dsWHA5dWpaV1RNYUVtckxSemd4dUdMdStQN2dtQjJTVHd4cEtlNHkwSDk0SEVZNUJLcE5Ub3FweUdtOVJ5bGdOakpxL1pmbmk4c3Y2dHhDcThpOE9aaVpEbXEwR0kwZEpQNGwyYkk2cmNUMjJQOUFnTFlFQVkzSVNqUjVMaXhSb3ptSG4vT2w1RitDZ0Yvb09rNmxpTHpYbk1EeFRpNDNabUxwWFdqUXMyVXpPUjBZb3Q3anhYRHZWUGlGaHFZVEFoclVPUU9QdzMzNDFMeTJvM1FjVkp3WHlnUVdIMkJLZnNLR0NDWTU1U1AyY0NQRHZGemQzU1RyMk51VFRQYXlLSUZ4S3NyZm9ranlCMVFMdnVEK0tEM0ozVWZCc1hib3BBYkxNeXhKN2Vla3lZbEVMbHZ5MnE5R1Z5aHFoZGdacHYvcHBHQTRreDlaT0gwSTEvbXRYL0tIa0o2SXh5M3pIek1rNGFNaVlIK3ZPMnY3SWoyM1JNM3FTZEdZdnJqdjUxejM3akxxNHVFQVBVQjJwZ2FyUXNVb3lRTTU0VnRpU3lmS21Eak9TNVMwSWFyY1A0Nk1CZUtHdm9jMUduUEZqMHp4T0FRODk3Vk5jcUhyVUZvQVRWeTlzczMiLCJtYWMiOiJjMjMwYzAzODkwN2IxN2NiNTBjMGJhMWE5NjNlNjUzZDJhZGMzYzlkM2Y2MGNiNmFjMWNkYTg2ZDg2MDQyMjU1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:13:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik1mTUxCVlRrckwweWg0N08zaWJJWlE9PSIsInZhbHVlIjoiQUN0K21LSVk0bmVRRDRyK0o1V2NJSFc1K2lBRTZDY0haTSt0ZW1HRDVYM3pmQkltRFJGdS9tNFFPcEtpd3IrOGI2bVFjbldVdm43Rk5TSHVHZ3hnWDRiZWdPTVBpc0V5S2V3aGxGc2k3aDRVOThtZWJlZ2E3QUJIdHkwZjVQQjhZNW8xQWZETjJYdGh2R3NaMUY1THYrcjNEOWNTMFlIVGNoSGlXT1VjRU5FTXBaK1JHMVB2b2NUcGlOV2hnSllhNEhRZEJjZE1tdS9jdVk2YllYMnozbjFUdEZXYnlHVmszNzJ2bitiV3Y3OVFKRno4THZwVzJwSm5Ga0wxNW9peUpGdHZoR1NIM21xdE1rMmZGNTJNL2kzL2YwMGdzaTFJN28rSUZiV2o2VWg2TGRoUHI3dkZic2tqc29DcDI0b0RhU2tlOERWRDl0RVZmMDIvOTB6YzdSZWFsYjlXR0l4Zk5jY3g5MVp3QW9wdnZxczZLNVd2UW01aTBBaVM0Z1cvYkhBRFFkQ0h6ejU2UHArVjVjUW5HaHNkOUF5cmRaNE5YcXRZYUloOFZlMWNDVXlBMUdlbUhZN3JaelI4T3VBY3hMbHM2eE40b08wZDZiaS9zN2pNa25EZUU5M0V3L0VsYlZoR3NCY2lNOENBSCtHM090UE9KODZETzM5dEM4V2UiLCJtYWMiOiJlMGFiMGU3ODAxN2ZkOWVkMjNiYzQzMmQ0MTI3NjZmOGE4NTJlN2I4NzI2ZWRkNmM4Zjc4NDBiOWFmOGNkNjM5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:13:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iml2VS9FMGVXbjlBdTA5ZzVOQ3FhTHc9PSIsInZhbHVlIjoiYW5oL2kxZGdKY0hVNzhzb1g2YnRHVXEyL2gybzdSYnJLSjRMb2dsWHA5dWpaV1RNYUVtckxSemd4dUdMdStQN2dtQjJTVHd4cEtlNHkwSDk0SEVZNUJLcE5Ub3FweUdtOVJ5bGdOakpxL1pmbmk4c3Y2dHhDcThpOE9aaVpEbXEwR0kwZEpQNGwyYkk2cmNUMjJQOUFnTFlFQVkzSVNqUjVMaXhSb3ptSG4vT2w1RitDZ0Yvb09rNmxpTHpYbk1EeFRpNDNabUxwWFdqUXMyVXpPUjBZb3Q3anhYRHZWUGlGaHFZVEFoclVPUU9QdzMzNDFMeTJvM1FjVkp3WHlnUVdIMkJLZnNLR0NDWTU1U1AyY0NQRHZGemQzU1RyMk51VFRQYXlLSUZ4S3NyZm9ranlCMVFMdnVEK0tEM0ozVWZCc1hib3BBYkxNeXhKN2Vla3lZbEVMbHZ5MnE5R1Z5aHFoZGdacHYvcHBHQTRreDlaT0gwSTEvbXRYL0tIa0o2SXh5M3pIek1rNGFNaVlIK3ZPMnY3SWoyM1JNM3FTZEdZdnJqdjUxejM3akxxNHVFQVBVQjJwZ2FyUXNVb3lRTTU0VnRpU3lmS21Eak9TNVMwSWFyY1A0Nk1CZUtHdm9jMUduUEZqMHp4T0FRODk3Vk5jcUhyVUZvQVRWeTlzczMiLCJtYWMiOiJjMjMwYzAzODkwN2IxN2NiNTBjMGJhMWE5NjNlNjUzZDJhZGMzYzlkM2Y2MGNiNmFjMWNkYTg2ZDg2MDQyMjU1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:13:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik1mTUxCVlRrckwweWg0N08zaWJJWlE9PSIsInZhbHVlIjoiQUN0K21LSVk0bmVRRDRyK0o1V2NJSFc1K2lBRTZDY0haTSt0ZW1HRDVYM3pmQkltRFJGdS9tNFFPcEtpd3IrOGI2bVFjbldVdm43Rk5TSHVHZ3hnWDRiZWdPTVBpc0V5S2V3aGxGc2k3aDRVOThtZWJlZ2E3QUJIdHkwZjVQQjhZNW8xQWZETjJYdGh2R3NaMUY1THYrcjNEOWNTMFlIVGNoSGlXT1VjRU5FTXBaK1JHMVB2b2NUcGlOV2hnSllhNEhRZEJjZE1tdS9jdVk2YllYMnozbjFUdEZXYnlHVmszNzJ2bitiV3Y3OVFKRno4THZwVzJwSm5Ga0wxNW9peUpGdHZoR1NIM21xdE1rMmZGNTJNL2kzL2YwMGdzaTFJN28rSUZiV2o2VWg2TGRoUHI3dkZic2tqc29DcDI0b0RhU2tlOERWRDl0RVZmMDIvOTB6YzdSZWFsYjlXR0l4Zk5jY3g5MVp3QW9wdnZxczZLNVd2UW01aTBBaVM0Z1cvYkhBRFFkQ0h6ejU2UHArVjVjUW5HaHNkOUF5cmRaNE5YcXRZYUloOFZlMWNDVXlBMUdlbUhZN3JaelI4T3VBY3hMbHM2eE40b08wZDZiaS9zN2pNa25EZUU5M0V3L0VsYlZoR3NCY2lNOENBSCtHM090UE9KODZETzM5dEM4V2UiLCJtYWMiOiJlMGFiMGU3ODAxN2ZkOWVkMjNiYzQzMmQ0MTI3NjZmOGE4NTJlN2I4NzI2ZWRkNmM4Zjc4NDBiOWFmOGNkNjM5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:13:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256680527\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-388055522 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/expense/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-388055522\", {\"maxDepth\":0})</script>\n"}}
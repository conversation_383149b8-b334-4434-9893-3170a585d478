{"__meta": {"id": "Xfdf9ac64f99b2a58ceb3ddd9e1b0df94", "datetime": "2025-07-29 17:16:57", "utime": **********.424236, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753809416.427798, "end": **********.424274, "duration": 0.9964759349822998, "duration_str": "996ms", "measures": [{"label": "Booting", "start": 1753809416.427798, "relative_start": 0, "end": **********.254729, "relative_end": **********.254729, "duration": 0.8269309997558594, "duration_str": "827ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.25475, "relative_start": 0.8269519805908203, "end": **********.424277, "relative_end": 3.0994415283203125e-06, "duration": 0.1695270538330078, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46595248, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.360239, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.373956, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.406711, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.028560000000000002, "accumulated_duration_str": "28.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.30649, "duration": 0.01681, "duration_str": "16.81ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 58.859}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3270872, "duration": 0.0067800000000000004, "duration_str": "6.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 58.859, "width_percent": 23.739}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.340749, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 82.598, "width_percent": 2.696}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3611841, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 85.294, "width_percent": 2.941}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3750489, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 88.235, "width_percent": 3.256}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.390917, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 91.492, "width_percent": 2.941}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3965511, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 94.433, "width_percent": 2.906}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4005568, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 97.339, "width_percent": 2.661}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OgWXawEpfTyIkkXpFmQITbWaBxUy8oPEY6KOarba", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-712158177 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-712158177\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2082568537 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2082568537\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1478156289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1478156289\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1772961950 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772961950\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-915691209 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-915691209\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1759704611 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:16:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imx5ZFdwaGJDc2Q5MkZ6UkNnTmJ3dlE9PSIsInZhbHVlIjoicVNacndDTkRvelhieGhmZ3UyS1ExcUpsNm9tZFcvdzF2ZG9RMUNPYXRzYlR1RHpUTHBQcW15aDhOMnlnK3BQaDBjT1BkWWoxUTM5dkpHN1NNOUtQMGF3Q3dKK2F2a21xa3pmekhIVkN2aURSY2RaNkVvRGtZK1JSMGxEdCtGYnhpZyszZVNDZHRoRkxIZHNlOXZteUNIcVZjdzRTeFBiTkREK3dLengzNnB3N1hvaURJY2VoZ1owbUI0UzJCU29IUzVnNWVpU3NKdmpCbXR2eUFobFg1WnRHeUt1WGd5V3NXKzdhVjFTVE1YRFBoZHlZcU82K1IvWEpWQy9wTWZidlpQZGUzdzVndmNDS1dvQWU3R0tQYmthVkdZVDFqRXIwUmVHOVNSNnhzNTR0VStGRXJobnpuS1NsTlVSYXBCODdGVTdIcWZnMXBrRWlpbTJ2enNCaDlrelgrQXIvUlROSFZ6SFVsUUVaTmttRFVhZ0F6dkRJMEd4T3NndldyQWRwYVJPYTlEbm5DR2kzdWZuckxiRlhYM1BPeHBEakZ1UWZtQ1I2eFZkcjNWWExvS2NNZ3FkUktpeU1iZkNoUUxpcmJHZWN3emxvMnpUWnBqSXBBV1hlQlp0SGhFandKdExZb0J6M1hKVTRKYmJudlBvbHdFNy8wVjlkWFJEeE5lN2giLCJtYWMiOiIwNWEwZTQ4NmUwYjQ2NWFlYjQ3ODllZGI1ODNmNTc0YzNjNzQ2ZGZmNTljOTAyZjc3ZDdjMzlmYjYyNWQwOTRkIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:16:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InhzdWxiUGFxVGpDaXF6c2VYSHhiK2c9PSIsInZhbHVlIjoicE9VRm4vMmpaN3ZENU12V1dTV0FyTHE4b0JjUmdPS21vT2VSczk3OGhNQWJNaVcwQThCQ0JnMEpCeHN2dUxkb3hjMWRZbVpTYk9aRXBnOHhtTzF4YjQ4TUFBY0VUM1M2akVXSjRXMmVBeUUxUHFsaURleTFXUFkvMHVIbG9QQmZTSU9oRHZqaVVzRVovYWxKdWROdmRKUUFTU0tmN3ZENzIzYk5SVHJVSWdmWnFKTTZKU0hPZG1yNC9oNTB5UXQxZkZXTmJXVjRqVE9UUXNZRWxiR1JPNVNPUUVqMXBPbFp3K1MrRThLL1lkMTUrcTg3cUM4UE5JK282VGl0RVIybzgvSU0xSkQ2bU42WklkSWRnSzF5U0d2MjFKSGdSaW1YUWtFMFJUS3YzbnF6L1pJMlhiSXQzMUNyMklwYmNlZXgyREozanhvdHVLQVpHOWtTTDZ2R0JtcUllVkFyZklzZWEyZDRLQ09uT2Jib2hFNUF4TDA4Y1lBRzhZTkdWZ1QxdnhYSFhnL094Q0MyWUxyMXNRdWtnQVpzaG1jV1luc2ZCRGFZclRsMmVKaFYxMEVsTGhqY0RsTGNDTERua1dTMlRzNERLUkRDZkpEay9LY0pzQXZrZ014eW9NaitvVFJkNmlrOW9DTEFqczhjVFY2NWNCV0cxa0IycmJZUjdmK1AiLCJtYWMiOiIwZDZkNDU0NWE0MTE3NDZmZTM1OTgwYTRmZDgzZDIxYzMwZmQ2NTA3ZGYwYmRjZGZjYTllNTJkZTRlY2JiZmJmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:16:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imx5ZFdwaGJDc2Q5MkZ6UkNnTmJ3dlE9PSIsInZhbHVlIjoicVNacndDTkRvelhieGhmZ3UyS1ExcUpsNm9tZFcvdzF2ZG9RMUNPYXRzYlR1RHpUTHBQcW15aDhOMnlnK3BQaDBjT1BkWWoxUTM5dkpHN1NNOUtQMGF3Q3dKK2F2a21xa3pmekhIVkN2aURSY2RaNkVvRGtZK1JSMGxEdCtGYnhpZyszZVNDZHRoRkxIZHNlOXZteUNIcVZjdzRTeFBiTkREK3dLengzNnB3N1hvaURJY2VoZ1owbUI0UzJCU29IUzVnNWVpU3NKdmpCbXR2eUFobFg1WnRHeUt1WGd5V3NXKzdhVjFTVE1YRFBoZHlZcU82K1IvWEpWQy9wTWZidlpQZGUzdzVndmNDS1dvQWU3R0tQYmthVkdZVDFqRXIwUmVHOVNSNnhzNTR0VStGRXJobnpuS1NsTlVSYXBCODdGVTdIcWZnMXBrRWlpbTJ2enNCaDlrelgrQXIvUlROSFZ6SFVsUUVaTmttRFVhZ0F6dkRJMEd4T3NndldyQWRwYVJPYTlEbm5DR2kzdWZuckxiRlhYM1BPeHBEakZ1UWZtQ1I2eFZkcjNWWExvS2NNZ3FkUktpeU1iZkNoUUxpcmJHZWN3emxvMnpUWnBqSXBBV1hlQlp0SGhFandKdExZb0J6M1hKVTRKYmJudlBvbHdFNy8wVjlkWFJEeE5lN2giLCJtYWMiOiIwNWEwZTQ4NmUwYjQ2NWFlYjQ3ODllZGI1ODNmNTc0YzNjNzQ2ZGZmNTljOTAyZjc3ZDdjMzlmYjYyNWQwOTRkIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:16:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InhzdWxiUGFxVGpDaXF6c2VYSHhiK2c9PSIsInZhbHVlIjoicE9VRm4vMmpaN3ZENU12V1dTV0FyTHE4b0JjUmdPS21vT2VSczk3OGhNQWJNaVcwQThCQ0JnMEpCeHN2dUxkb3hjMWRZbVpTYk9aRXBnOHhtTzF4YjQ4TUFBY0VUM1M2akVXSjRXMmVBeUUxUHFsaURleTFXUFkvMHVIbG9QQmZTSU9oRHZqaVVzRVovYWxKdWROdmRKUUFTU0tmN3ZENzIzYk5SVHJVSWdmWnFKTTZKU0hPZG1yNC9oNTB5UXQxZkZXTmJXVjRqVE9UUXNZRWxiR1JPNVNPUUVqMXBPbFp3K1MrRThLL1lkMTUrcTg3cUM4UE5JK282VGl0RVIybzgvSU0xSkQ2bU42WklkSWRnSzF5U0d2MjFKSGdSaW1YUWtFMFJUS3YzbnF6L1pJMlhiSXQzMUNyMklwYmNlZXgyREozanhvdHVLQVpHOWtTTDZ2R0JtcUllVkFyZklzZWEyZDRLQ09uT2Jib2hFNUF4TDA4Y1lBRzhZTkdWZ1QxdnhYSFhnL094Q0MyWUxyMXNRdWtnQVpzaG1jV1luc2ZCRGFZclRsMmVKaFYxMEVsTGhqY0RsTGNDTERua1dTMlRzNERLUkRDZkpEay9LY0pzQXZrZ014eW9NaitvVFJkNmlrOW9DTEFqczhjVFY2NWNCV0cxa0IycmJZUjdmK1AiLCJtYWMiOiIwZDZkNDU0NWE0MTE3NDZmZTM1OTgwYTRmZDgzZDIxYzMwZmQ2NTA3ZGYwYmRjZGZjYTllNTJkZTRlY2JiZmJmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:16:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759704611\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1548177464 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OgWXawEpfTyIkkXpFmQITbWaBxUy8oPEY6KOarba</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1548177464\", {\"maxDepth\":0})</script>\n"}}
{"__meta": {"id": "Xd3cb7e120a82affa6b8b63505f59b468", "datetime": "2025-07-29 17:28:34", "utime": **********.973923, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.085896, "end": **********.974003, "duration": 0.8881070613861084, "duration_str": "888ms", "measures": [{"label": "Booting", "start": **********.085896, "relative_start": 0, "end": **********.897013, "relative_end": **********.897013, "duration": 0.8111169338226318, "duration_str": "811ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.89705, "relative_start": 0.****************, "end": **********.974006, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "76.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NfCqRPsVyDZ318zV6IXfEiaIF7rOwxOGjjrMUYPX", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1824989125 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1824989125\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1290183855 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1290183855\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-344616980 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-344616980\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1240193973 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240193973\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1527914874 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1527914874\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-729408589 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:28:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJqK0lzbkNpWU5tT3B5cDIrSzFja2c9PSIsInZhbHVlIjoiakM5ZjFtSHZ0NWNjd004OG9WMGFDeFNMdHRuSlhUcHFzeWtnYnZtSXR6VFBhaFRuNmE0cG9hK1k5YnhSR0pxMkx3T2RPY1pJN1JPektYZnA5M3NhMWhaNmh3K3JkeUdLVXZ4V1Fkb2wreDNHMGxhNG1ORVRlZyswMzRleTRSem9MTmRRa0E2RFl3VnFuQXc0bU5QbTlPUUtlKytUQmsrOXk2RUxOSWdhQlpqNlY2bG9wZGhoRXZXVys2Q1RBZWY3T09JclQrNEV1V3FLRHBQQnpJc0tudTQxNjlyY1k2WnJOc2gxMkI1Skh1SFVZd2ZuVjQ1TURWZjFBZVJyZTcvR0MrVkloaEZ4eXEvVEtzRDExN0czRlh2QjhoSkdjRjhaTUFweVRTaUU0TEJSYjhNUGFyd3RPUHdHZ2VZYmJxQWwxbGY0QkdEbStqQ0oxNkg2d1BBL24zck9rc1hueGE0em12VzYyV2xiVWlVQmVWaUpPeTlLaTE0cHQ3SWx2TnlXZG9xb2lxaVRtVitzd1JWclJ2SjB5UXFMdFp2aVVLVHIzdUhIMTJodFN2Z3V3UkM3NFhtZElpNTVlRk1OQVkyTk1wblRmSWYwUUY4MzZIK2JKWWJ0QXBmWmlhOWxMMzFzSTF5a3RwU05UeUxuNzRRUXZUcEtvb3l6YURpa21Ua3IiLCJtYWMiOiJhOGY1NDNhOTdhYzhkNWY4NjhlYmEyM2MwYzU3MmNiZTI0NTVjZTVjMzcxZTEzN2M2MmJjNWZjNGFiMDhjNGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:28:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjAwQ3JCTnQrYUFqakEyTDRoQzMxMmc9PSIsInZhbHVlIjoiejllem9QV3dLbG5uTUJLOVMzcnYrazRRWXJiRG1oclovZ0ZWVTRXbVd4MlpVTk5yWTRUamZqWXk0RDNhVWErdVBxanhmTkwxZXlKSDZoSlptUnpDS1BPc3VuOUJoNHh5R1ZuR3Y3dHJ5elhETXhLR3pYVTJtdXpzbS9wU2lQRmdQaVRZNm1NY0xIYklSSlZxaTRUU0ZaK2owUHBmNXBrQ1RzU1FqUHVPQm9IMjdMaW9SWUV0eWNMd2ROWjY4YTZvWnA5ekdoWXV1U3h6OXFGeWpVNm9KMnFUaXIyUEpTaUJINTAwV0Y1Q1dTM0dwbDNPM3d0Nnp2SEdNVUpKWVBZbWtjbTc3bmVvWXBPUUpqQ2lHczlodGpFbHdyMkNYNEVCYnRaYjRQQmV0TXloUTJvNlk0UzlxajlDVHh5MVphNW9OUHhVQkdrTzQvYlpYMDNNRGgvZitRTWFDd2gwTVExUVFIblpiRittYWhkMU1oM05wRG5XdjdiRHF2bzl0TExJOHZGOERRSnYvUzhGOEU5WEJoc21SMVNvS1BBSFdlakZYQkErWjFmaXRydzNUQ2l4cC9oWUNXek0rMmpOZzBRTWorbU1peGZoSW1mamVMVm9ONWZOVjErL3BqcGR0MHpxYXdTTjdZQ3dTM3kyckZuVVVZYkZkVDVDN0I0TEVuU3EiLCJtYWMiOiI5YWZkOTk5OGM4NGJlNjE2ZjViNTk5OTllZDA0NmIxNmI5NDgyOWFiZTE4ODM2MGFjMmRiNjhhNTA2YzllMjNmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:28:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJqK0lzbkNpWU5tT3B5cDIrSzFja2c9PSIsInZhbHVlIjoiakM5ZjFtSHZ0NWNjd004OG9WMGFDeFNMdHRuSlhUcHFzeWtnYnZtSXR6VFBhaFRuNmE0cG9hK1k5YnhSR0pxMkx3T2RPY1pJN1JPektYZnA5M3NhMWhaNmh3K3JkeUdLVXZ4V1Fkb2wreDNHMGxhNG1ORVRlZyswMzRleTRSem9MTmRRa0E2RFl3VnFuQXc0bU5QbTlPUUtlKytUQmsrOXk2RUxOSWdhQlpqNlY2bG9wZGhoRXZXVys2Q1RBZWY3T09JclQrNEV1V3FLRHBQQnpJc0tudTQxNjlyY1k2WnJOc2gxMkI1Skh1SFVZd2ZuVjQ1TURWZjFBZVJyZTcvR0MrVkloaEZ4eXEvVEtzRDExN0czRlh2QjhoSkdjRjhaTUFweVRTaUU0TEJSYjhNUGFyd3RPUHdHZ2VZYmJxQWwxbGY0QkdEbStqQ0oxNkg2d1BBL24zck9rc1hueGE0em12VzYyV2xiVWlVQmVWaUpPeTlLaTE0cHQ3SWx2TnlXZG9xb2lxaVRtVitzd1JWclJ2SjB5UXFMdFp2aVVLVHIzdUhIMTJodFN2Z3V3UkM3NFhtZElpNTVlRk1OQVkyTk1wblRmSWYwUUY4MzZIK2JKWWJ0QXBmWmlhOWxMMzFzSTF5a3RwU05UeUxuNzRRUXZUcEtvb3l6YURpa21Ua3IiLCJtYWMiOiJhOGY1NDNhOTdhYzhkNWY4NjhlYmEyM2MwYzU3MmNiZTI0NTVjZTVjMzcxZTEzN2M2MmJjNWZjNGFiMDhjNGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:28:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjAwQ3JCTnQrYUFqakEyTDRoQzMxMmc9PSIsInZhbHVlIjoiejllem9QV3dLbG5uTUJLOVMzcnYrazRRWXJiRG1oclovZ0ZWVTRXbVd4MlpVTk5yWTRUamZqWXk0RDNhVWErdVBxanhmTkwxZXlKSDZoSlptUnpDS1BPc3VuOUJoNHh5R1ZuR3Y3dHJ5elhETXhLR3pYVTJtdXpzbS9wU2lQRmdQaVRZNm1NY0xIYklSSlZxaTRUU0ZaK2owUHBmNXBrQ1RzU1FqUHVPQm9IMjdMaW9SWUV0eWNMd2ROWjY4YTZvWnA5ekdoWXV1U3h6OXFGeWpVNm9KMnFUaXIyUEpTaUJINTAwV0Y1Q1dTM0dwbDNPM3d0Nnp2SEdNVUpKWVBZbWtjbTc3bmVvWXBPUUpqQ2lHczlodGpFbHdyMkNYNEVCYnRaYjRQQmV0TXloUTJvNlk0UzlxajlDVHh5MVphNW9OUHhVQkdrTzQvYlpYMDNNRGgvZitRTWFDd2gwTVExUVFIblpiRittYWhkMU1oM05wRG5XdjdiRHF2bzl0TExJOHZGOERRSnYvUzhGOEU5WEJoc21SMVNvS1BBSFdlakZYQkErWjFmaXRydzNUQ2l4cC9oWUNXek0rMmpOZzBRTWorbU1peGZoSW1mamVMVm9ONWZOVjErL3BqcGR0MHpxYXdTTjdZQ3dTM3kyckZuVVVZYkZkVDVDN0I0TEVuU3EiLCJtYWMiOiI5YWZkOTk5OGM4NGJlNjE2ZjViNTk5OTllZDA0NmIxNmI5NDgyOWFiZTE4ODM2MGFjMmRiNjhhNTA2YzllMjNmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:28:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729408589\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1183165204 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NfCqRPsVyDZ318zV6IXfEiaIF7rOwxOGjjrMUYPX</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183165204\", {\"maxDepth\":0})</script>\n"}}
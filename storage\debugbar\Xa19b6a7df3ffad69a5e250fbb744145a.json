{"__meta": {"id": "Xa19b6a7df3ffad69a5e250fbb744145a", "datetime": "2025-07-29 17:42:26", "utime": **********.362834, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[17:42:26] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.356981, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753810944.987491, "end": **********.362858, "duration": 1.3753671646118164, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1753810944.987491, "relative_start": 0, "end": 1753810945.999786, "relative_end": 1753810945.999786, "duration": 1.0122950077056885, "duration_str": "1.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753810945.999807, "relative_start": 1.0123159885406494, "end": **********.362861, "relative_end": 2.86102294921875e-06, "duration": 0.3630540370941162, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48347656, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.08898, "accumulated_duration_str": "88.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.104557, "duration": 0.0205, "duration_str": "20.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 23.039}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.142117, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 23.039, "width_percent": 0.652}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.146514, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "radhe_same", "start_percent": 23.691, "width_percent": 0.551}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 84 or `ch_messages`.`to_id` = 84 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["84", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.152113, "duration": 0.01611, "duration_str": "16.11ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 24.241, "width_percent": 18.105}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 84", "type": "query", "params": [], "bindings": ["client", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 364}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.174977, "duration": 0.032159999999999994, "duration_str": "32.16ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:364", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=364", "ajax": false, "filename": "MessagesController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 42.347, "width_percent": 36.143}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.2341669, "duration": 0.00529, "duration_str": "5.29ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 78.49, "width_percent": 5.945}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.254632, "duration": 0.00477, "duration_str": "4.77ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 84.435, "width_percent": 5.361}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.266835, "duration": 0.0057599999999999995, "duration_str": "5.76ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 89.795, "width_percent": 6.473}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.278168, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 96.269, "width_percent": 3.731}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 542, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 545, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-273313738 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-273313738\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-50912738 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-50912738\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1791104324 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1791104324\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-931790443 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2718 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; omx_ai_suite_session=eyJpdiI6Im02aWF1ZnE3RTQvSmtiMW5BcERQV3c9PSIsInZhbHVlIjoiUGE4L1gwR29Ya0tIc2pGYVovTzZId2k2TGFSdnRLV21sK1AyMlNIZVRoOVdjaU1jN0Robk5HZ2doSkpmY0FXemFhUzdMQjUxNmZCY2xtcDF0ODBvbVpNQmJtL05WWGFTQnRrcDF0R09NUG0yV2x0VlhQMk1TSXpWU1lWY1FFSEhuNHlidlN4V1BQVkh2ajNIb3VYZk1LNjRzOXFXc1Y4MU1jQkh3Y0xwYWU3NkxXMmFYZENJSldkbFJzTWJKSE5pQ25oMGIrMGVMNFdjMG45ZEI5b1Z4NjR0M0EvWjBVSHZyVW12TFgvZ2ZuOEVEdlR1Wi9DRUxvWnp2cXlTZk1LWDJqVmcwZExoZThzM0YzNWVUdmV0Z2tmQmc1UFdrMHpkVDhQdWhIMmxVN3J4MGtOOUZqRm1QcG1yK2Z6STcreUcxcEdnLzNDaTNVakcxdlJiVWdGQXlLV3BKejdidXBPM3ZNcU9EQkZWaG9mTkl1Z2Jjb0JwK2wvOUJRYjNYN2ZjYlJCU3ZUSVRhc29CMFdCR3FRY09wQkdML25lYXp6bzFDTG5EWS81TnowdUxVMVpLUUJsUEhxNUJkZDJGZVFWVENkOC9ETytPaHBiSzE2RUdkRkdTTFc0SGZURzlCVjBteEZrSDJPWUtVVmNTQ1RoRGg1TDMvdW8yYkNuNHhPZGsiLCJtYWMiOiIzZDU3ZjdlZDM2MDg5ZDJhMWRkMDMxYjk2ZDhjNGY1YTY3MmE3YmNlYzRjYmM3YmQyZTBkMThhZGFlZDEwYjIwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InlleVBjR0k5UmFkN3FYdXl3aFhMRVE9PSIsInZhbHVlIjoiQzFBQ0NHRVVzaUNCalZETVhwSmVFWGZOeEdIT3YraTM4N2xYVzhNcFltT0J5Y3JoUXEwWUJkOUtOcEI3SVpSY3ViVXNCMlQ4M3ZUSHd5clhQQ1dmekVJOFk3b1dHRTU5a2RlVUJpUlhuRHh5UGljUTBVQWNQSXRYS01hQ0tKajlwUE1VaFdtSjFjS09DeWZQOWcybG1pMVVxZFZZbEU2SDREUE80MCtpMnF4UTlWMm9YVFhEdFF5QlFPTjlQSFl6eFVtZk5TUmQwUlB4cmJBZ2hyVkhtenhaM1llaXFvWkoybThGRkNmUGc3bVR5V21XRXBPYW5DSlNzSkpRV21mRDlzaDhGeHU1NUJaSHpyU1FnVVZueFBTK2hMS1ZKMEFodzNBUURUekZaTDU0L2NqSCtxSytpbVNhZ0h5citQZXQyMnVVandSbTJKK3MyeFVtNEFEZnloTHpKeXRKeVpwTytnalJTdGxJL2NpUURkWEFZdDVHZHQ1YzMwVnUyVUNCTmx0UFU2ZEFCbkpySWlicmFheXFMeTlQL0tmRlBsejRSL1FhZkNQbWJ3WUZVTmhjTHpnamdLWEtNb2pFWDhaYUhLNmgrVkJmakpaWm9iR1BzektiTVJURG5ocit0OTRDdVV1UkZ3WXdvbXVSeVpOdVZPb3NWemplK3JycFB2d1MiLCJtYWMiOiJlZjBiMzU0ZjJiNDNhMWM2Y2UzMzNhMzlhNTJkOTgyMWFlMWI1MjZiZWQzODlmNDVlNGY4ZDAzYWE0NTVjOWUzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImlDcDhlYi95QitaTW4xMnpicGE0RWc9PSIsInZhbHVlIjoiNVpUSzVJeHFuTXZ3MkxJcksxRGg2d0FmcHNzSlE3OW1HZHdqSW1hWEFhV0s4aFN1M1RNVzFDOXVZSzNRT3N3YmhHam0zRytpa2dHZWRrc3E2aXlKbTZhOW96SlJkL04xVVpjSlNzUWtlRzEyQnZGNFdiK0Zia3RRWStVSEJoblpRRHNoQTZqMzFlRmR5UFBYQ0tlL0ZzbFdybC9Xa0hRT1dOaklzcWF6WldKT2FDdFFKSzVUT0RDQ291M2NXVVFidWdTckN3QUNIbjA3d1lEUVkwVU82MkdyVnZ1VHZjWmIrZlgrOGhrdDFHRC9JUXg5TzZuenU5ellNcHFhVWpyUk0ybWt6TDh4VWp6bGQ2dUZNc0U5bTdhcTBwSXNFd041ZmUzNUxQbTd6bWNSODdGenVsUnQ4VGNWS003a2JRUzA5Q1BzNVBva1Z5TjdOMExURDRkWHJBV0Erc3VYZnRmV2NEMHFlQW9yb3JJdlkvRlFXMWF2a2YrMWZLa3pCcnhkcFltTGpRczM5MzU0VDhuOXdhOFJjUXpZUnV5MnNqQy8vdG53VjBtNVZiSCtIMUVtN0xkTkJ3b2VQUG9FNHR2WVYreW9hZktNVU5iaEtjY0Z5Zk5hMVVKaHkrc1RTUHA0NmM5a2JvbzFqcjJMYmRJV1o1OVU1Y0hRVis1eGRrRnkiLCJtYWMiOiJlMmNjNjNhNDlhYTE3MjYxMGVjZTIzZDFjZGJiNWE5YWFiYjUxZGM5ZmUzZjg5ZTRkOGYwMzM4ZTg1MTAzODZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931790443\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-804888348 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WnT8J6dr1q3cGe2zznYjRzgdZFNO79ma6VzwnyJd</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MYGtST6gzPoxGtZLL5j826OymweTDicqK5wxV4qK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804888348\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:42:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRLRzhzRi9Hc1FCT0pCa1F6MU1HWUE9PSIsInZhbHVlIjoiazRsVWhNQXBZT3VWZHlHdXYzVnZzTis4ajNHWDJXZ2sxaGF0RVhEd2tPdkZpRjA2OHZPb0c4eXV4UCs4TUdOSWgyaE9GMEJhYVdhUUdkK0c5ays4T1c3R0RjMGU2RHhEM0dNV2R5allSZEpHWnJ5UFN2OWpqWGhjYmd3SlhoRHlUMkNkRlBCVzVQN0lVVXNpalp5anJjYm42c3dheVAwOFZCSWRVRWROUklIaUtmRXpuSFYyQWU0QWh1aHFzUTAyb1NSV1JPWmt2eXBtRlE4K1FOQ3VNdWpWSHhiek04TkwwenZvWFdSYUpkRWZBZktlNjA3REg1VzlCVEkwbUpsNStNK2pEREVBZjMwdUpxd1ZTNXBXYUx5dk9rODBJOGdoMXk5cC9Cb2hWNk9zNEZzRkhqNzArRjM4MXJZQ01YUUpPczc2b1RhWkNyRThJVndmU2k2TC80SGU5dk96SEJqVG9DVUlmL1pmVXdwdmRQRTlrbjMxM2phS0s5MVlxb290UjhISHBXQXhndkErdnFjR2ZHaHp3YmVCZllnNk13SDQ2ek9PQmF1SGRkQ2FMb3dCVnhZZlUzQ1ZIZHNtTHlka3cyRVRJM1NFTGo5WU1nYmFveDRYQjM1eE0rRFVaSXFXWkJmbzQ1Z056aDAwZEttZnlLSnZIcmk1UUJpN0pqTTAiLCJtYWMiOiI1ZGZmMjllOTE3ZmFmN2U5MTBmNmQ5YzdmMDlkZjM1YTJjNTA1OWRjYjUyNjA0MDY2ZDYwNDllNDJjZWQ1OWUwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:42:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InJvcFhTQW45Rk1PZkI4ejVuSGRrMkE9PSIsInZhbHVlIjoiMnE4ZmFEMFJNRExWWVRCZWFNa082RHQrVURNT3Z1U3EwVXg0djRkYjZxU0k2OE9Yd2Y2ZjU3K2VGVllIL1dkVlJOdXJ3RTFVWEJzLzJLcFVVUXZqNTg4ZHk4WDhPaVlvM1ZoNjF1c3FvcWpCREx0SkI4bGY1MXRHRmtlVHo2MXl3RUhzWDFZVkRxeDlZQWdhVGJURWIzM2pqWUpXN0t1NFZrOU1WUzdYYWJRSGpnQ09vWHVPakl6YVY3R05Ic2x3SE9vbGh3WlltTGJnZ0QyUXNwMkkxc0dGZGRqZFpyZWhpWW9GeGJjS002a082SHd4RWtnbjFlTlQ2VG1qdlBJaWFtbCtFMFJCSkJ3L0N5ekJRa041akVaWjZLTExZK2pld2hIT2xGSVhkbUNNb2lNL1BsL0xOMHhpY1NnME0wZHVISjkwRVh6S3dkbVhnYmdOQmxaN2RFbGtCRnlCcUkvWlVuZTVncFdiUmJLWFFtbmlRa1M2K2tPRDdoVkc5TTl1TzZpL0FaRTVmbDVULzRZVWZITUNOaVpESjZ6OVE4RlhYOHZaVHRTZnJueDNQK0R4dGliWlJUZy93bFZZay9SbFZyVHlFQlVIOFBYL2JLdVUvWUREUGRCWCtoZzZLaS9wcDdWSGlZWEpxM3VrZkFjM1RlRTZwak5qYUt1SFJWL24iLCJtYWMiOiI1YTkwZjQ0YTU3NmJlODA4MTY4NzliNTM1ODY4OWRjZDFjMWQ3OTY5ZTBlNmM3MDlkNGQ1MjMxZmQxOGYxZDg4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:42:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRLRzhzRi9Hc1FCT0pCa1F6MU1HWUE9PSIsInZhbHVlIjoiazRsVWhNQXBZT3VWZHlHdXYzVnZzTis4ajNHWDJXZ2sxaGF0RVhEd2tPdkZpRjA2OHZPb0c4eXV4UCs4TUdOSWgyaE9GMEJhYVdhUUdkK0c5ays4T1c3R0RjMGU2RHhEM0dNV2R5allSZEpHWnJ5UFN2OWpqWGhjYmd3SlhoRHlUMkNkRlBCVzVQN0lVVXNpalp5anJjYm42c3dheVAwOFZCSWRVRWROUklIaUtmRXpuSFYyQWU0QWh1aHFzUTAyb1NSV1JPWmt2eXBtRlE4K1FOQ3VNdWpWSHhiek04TkwwenZvWFdSYUpkRWZBZktlNjA3REg1VzlCVEkwbUpsNStNK2pEREVBZjMwdUpxd1ZTNXBXYUx5dk9rODBJOGdoMXk5cC9Cb2hWNk9zNEZzRkhqNzArRjM4MXJZQ01YUUpPczc2b1RhWkNyRThJVndmU2k2TC80SGU5dk96SEJqVG9DVUlmL1pmVXdwdmRQRTlrbjMxM2phS0s5MVlxb290UjhISHBXQXhndkErdnFjR2ZHaHp3YmVCZllnNk13SDQ2ek9PQmF1SGRkQ2FMb3dCVnhZZlUzQ1ZIZHNtTHlka3cyRVRJM1NFTGo5WU1nYmFveDRYQjM1eE0rRFVaSXFXWkJmbzQ1Z056aDAwZEttZnlLSnZIcmk1UUJpN0pqTTAiLCJtYWMiOiI1ZGZmMjllOTE3ZmFmN2U5MTBmNmQ5YzdmMDlkZjM1YTJjNTA1OWRjYjUyNjA0MDY2ZDYwNDllNDJjZWQ1OWUwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:42:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InJvcFhTQW45Rk1PZkI4ejVuSGRrMkE9PSIsInZhbHVlIjoiMnE4ZmFEMFJNRExWWVRCZWFNa082RHQrVURNT3Z1U3EwVXg0djRkYjZxU0k2OE9Yd2Y2ZjU3K2VGVllIL1dkVlJOdXJ3RTFVWEJzLzJLcFVVUXZqNTg4ZHk4WDhPaVlvM1ZoNjF1c3FvcWpCREx0SkI4bGY1MXRHRmtlVHo2MXl3RUhzWDFZVkRxeDlZQWdhVGJURWIzM2pqWUpXN0t1NFZrOU1WUzdYYWJRSGpnQ09vWHVPakl6YVY3R05Ic2x3SE9vbGh3WlltTGJnZ0QyUXNwMkkxc0dGZGRqZFpyZWhpWW9GeGJjS002a082SHd4RWtnbjFlTlQ2VG1qdlBJaWFtbCtFMFJCSkJ3L0N5ekJRa041akVaWjZLTExZK2pld2hIT2xGSVhkbUNNb2lNL1BsL0xOMHhpY1NnME0wZHVISjkwRVh6S3dkbVhnYmdOQmxaN2RFbGtCRnlCcUkvWlVuZTVncFdiUmJLWFFtbmlRa1M2K2tPRDdoVkc5TTl1TzZpL0FaRTVmbDVULzRZVWZITUNOaVpESjZ6OVE4RlhYOHZaVHRTZnJueDNQK0R4dGliWlJUZy93bFZZay9SbFZyVHlFQlVIOFBYL2JLdVUvWUREUGRCWCtoZzZLaS9wcDdWSGlZWEpxM3VrZkFjM1RlRTZwak5qYUt1SFJWL24iLCJtYWMiOiI1YTkwZjQ0YTU3NmJlODA4MTY4NzliNTM1ODY4OWRjZDFjMWQ3OTY5ZTBlNmM3MDlkNGQ1MjMxZmQxOGYxZDg4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:42:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}
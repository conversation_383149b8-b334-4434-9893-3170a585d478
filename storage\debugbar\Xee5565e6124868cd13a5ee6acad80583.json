{"__meta": {"id": "Xee5565e6124868cd13a5ee6acad80583", "datetime": "2025-07-29 17:33:00", "utime": **********.496588, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753810379.566235, "end": **********.496613, "duration": 0.9303779602050781, "duration_str": "930ms", "measures": [{"label": "Booting", "start": 1753810379.566235, "relative_start": 0, "end": **********.416174, "relative_end": **********.416174, "duration": 0.8499388694763184, "duration_str": "850ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.416187, "relative_start": 0.***************, "end": **********.496616, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "80.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "K7jtwHsOgtGLQVP1X9WpKqS4ilAFZAkhRmz3QN2k", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-354809592 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-354809592\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1449746389 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1449746389\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-625933356 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-625933356\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-411050264 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411050264\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-285666297 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-285666297\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1417333262 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:33:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFGTFVpNWV3aHRlNThoeERXYm9NV3c9PSIsInZhbHVlIjoiWjhuK2ZERVhBNWZyWDUzZCtxMVhmVmtzQUZZWWpUTXhrNTdVbCtSUUtaQUQ0c1I2N1U1OERSdWtLNU0vTHBodUY4Rm9MMVl3MU9GT3VKY3ZYaG9JSGE0czRhYTcxOFZRL2tkenlxSTFoaHJTTS9RNFVzOUx1UkpEZWxRSjlFaVE4Zzg1eFdRYjB3R083bFR4QjluY0k5OTFmNTFlWm5RS0V6dmNKTjU1S0R4Vkp0UFBLbHp4ZStvS0VhMnpjNlhPeXVCVDRDbi9JdDMwOVNRbFF4SXFJcjEwL0kvdnByT3dFdnFOempha2I2Z05NbjJxWnBva3h6aWZIcGVpaGJodTR1NTNYdkxWZzFPcUgydWloeUpBejRPMjdiclVnZUg3anpnTnZpekQwS0E0RDlXdEZyeVpsSTdEMWhmU3BzR21TV1pqMXVoNm9Uam9sL0tjZXNUU0tXVWNHczhISjFRT0ZwSXVjT1l1VlZocEREWXNPaXJ5SndQcGxqSkt0MnNzdmNxaVJMRjE5UU5MNUFZZ0JqMWpVaHZXWTVteVBrOEEwWktMbEVzWjZnRE9UeXVMUkhieFRKWUhJdmtBQ2ZyLy9maVRCTzRUdWJ4V2VsV1JnZXJRd3laem9UdXE5dTNmU2dxWk1tbzBHNjJkQURwdDVMWlpMbVhleHFzWU4wQksiLCJtYWMiOiJmMTMwZTJlYzA2Nzc0NDFhMTE4NGIwZDE2NDczYTVlODgxNDUxODAxMzcxOTZhY2UxODk1ZDAyMjk0ZDA2YWI5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:33:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjFiS2N4U2x2eldxWmdIWWpRWHpBWmc9PSIsInZhbHVlIjoiRS9KdUhEMGRvU1kxTUxzeEpja0dybXloVzhMVVhpQi8yRWRqZ2Joc3V2QjNSSTJEcVJnKzZ2SkVFNktBRy9VVjNMY25hSE53R05aTkFjNUliMTVxUllndktIUHhuRWpoRldBdVMxdnh0bTZXTXR1VU9pa3VWS25YcDVrOWErb3BHcXQ1M0ZOeS8xKzVYUFFDdDN6YXdVbm10MXR1M3RoV0tiQkkvbnZqNjd4UWdKdnhwZk9DY2pSc0xOMXdYUFZjUVltQmhEYmRWNUlVUmxmS0JOWWZhRUZqSTlmSXhGaE14WHFiblJla01xMEpkcytHNnlyN25jTEpGeWEydThaYWJqRkd4dURBZUJzVHpoT1BuczVRRDZvZ1lhT21PcTBveStBTGpBQjZCQ3Mxa09yTURUL3NKTkRvelRHQTRyK3Avc2V2UTZLWUJiQVdDbU5aWkswT3craklzRnZFbmJNNWc5ckRERnoycjVaZDhINVpPMjlEM0RxSWlDTUJ3QWFZaTNlWkViZ3AyNHFhNlVnWjFpRnRGNzdzZU1lMWpReWZjSloyMkkzMlB4R0N5bUR5ZWdnZ1ljL1pRUWdoaEF4a09neFNjRUtacGQyTmFlQlQzbnpObmVyZVRYZnpJdHg5Wi9LNkE3eC8rMWEyS3lyVVQwcmx0eGJqYU5QM3hrVTEiLCJtYWMiOiJhMTY0ZDljMjUzZjZlNDJjMGRjNWZlNGU5NDllNjg4MDRiZTJlMjUxMDQ2NDljNTEzZjViZDdjMGEyYTk3MzExIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:33:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFGTFVpNWV3aHRlNThoeERXYm9NV3c9PSIsInZhbHVlIjoiWjhuK2ZERVhBNWZyWDUzZCtxMVhmVmtzQUZZWWpUTXhrNTdVbCtSUUtaQUQ0c1I2N1U1OERSdWtLNU0vTHBodUY4Rm9MMVl3MU9GT3VKY3ZYaG9JSGE0czRhYTcxOFZRL2tkenlxSTFoaHJTTS9RNFVzOUx1UkpEZWxRSjlFaVE4Zzg1eFdRYjB3R083bFR4QjluY0k5OTFmNTFlWm5RS0V6dmNKTjU1S0R4Vkp0UFBLbHp4ZStvS0VhMnpjNlhPeXVCVDRDbi9JdDMwOVNRbFF4SXFJcjEwL0kvdnByT3dFdnFOempha2I2Z05NbjJxWnBva3h6aWZIcGVpaGJodTR1NTNYdkxWZzFPcUgydWloeUpBejRPMjdiclVnZUg3anpnTnZpekQwS0E0RDlXdEZyeVpsSTdEMWhmU3BzR21TV1pqMXVoNm9Uam9sL0tjZXNUU0tXVWNHczhISjFRT0ZwSXVjT1l1VlZocEREWXNPaXJ5SndQcGxqSkt0MnNzdmNxaVJMRjE5UU5MNUFZZ0JqMWpVaHZXWTVteVBrOEEwWktMbEVzWjZnRE9UeXVMUkhieFRKWUhJdmtBQ2ZyLy9maVRCTzRUdWJ4V2VsV1JnZXJRd3laem9UdXE5dTNmU2dxWk1tbzBHNjJkQURwdDVMWlpMbVhleHFzWU4wQksiLCJtYWMiOiJmMTMwZTJlYzA2Nzc0NDFhMTE4NGIwZDE2NDczYTVlODgxNDUxODAxMzcxOTZhY2UxODk1ZDAyMjk0ZDA2YWI5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:33:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjFiS2N4U2x2eldxWmdIWWpRWHpBWmc9PSIsInZhbHVlIjoiRS9KdUhEMGRvU1kxTUxzeEpja0dybXloVzhMVVhpQi8yRWRqZ2Joc3V2QjNSSTJEcVJnKzZ2SkVFNktBRy9VVjNMY25hSE53R05aTkFjNUliMTVxUllndktIUHhuRWpoRldBdVMxdnh0bTZXTXR1VU9pa3VWS25YcDVrOWErb3BHcXQ1M0ZOeS8xKzVYUFFDdDN6YXdVbm10MXR1M3RoV0tiQkkvbnZqNjd4UWdKdnhwZk9DY2pSc0xOMXdYUFZjUVltQmhEYmRWNUlVUmxmS0JOWWZhRUZqSTlmSXhGaE14WHFiblJla01xMEpkcytHNnlyN25jTEpGeWEydThaYWJqRkd4dURBZUJzVHpoT1BuczVRRDZvZ1lhT21PcTBveStBTGpBQjZCQ3Mxa09yTURUL3NKTkRvelRHQTRyK3Avc2V2UTZLWUJiQVdDbU5aWkswT3craklzRnZFbmJNNWc5ckRERnoycjVaZDhINVpPMjlEM0RxSWlDTUJ3QWFZaTNlWkViZ3AyNHFhNlVnWjFpRnRGNzdzZU1lMWpReWZjSloyMkkzMlB4R0N5bUR5ZWdnZ1ljL1pRUWdoaEF4a09neFNjRUtacGQyTmFlQlQzbnpObmVyZVRYZnpJdHg5Wi9LNkE3eC8rMWEyS3lyVVQwcmx0eGJqYU5QM3hrVTEiLCJtYWMiOiJhMTY0ZDljMjUzZjZlNDJjMGRjNWZlNGU5NDllNjg4MDRiZTJlMjUxMDQ2NDljNTEzZjViZDdjMGEyYTk3MzExIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:33:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417333262\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1295102433 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K7jtwHsOgtGLQVP1X9WpKqS4ilAFZAkhRmz3QN2k</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1295102433\", {\"maxDepth\":0})</script>\n"}}
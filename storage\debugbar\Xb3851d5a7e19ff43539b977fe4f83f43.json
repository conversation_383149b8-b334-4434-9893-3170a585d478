{"__meta": {"id": "Xb3851d5a7e19ff43539b977fe4f83f43", "datetime": "2025-07-29 17:23:20", "utime": **********.713099, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.089544, "end": **********.713146, "duration": 0.6236019134521484, "duration_str": "624ms", "measures": [{"label": "Booting", "start": **********.089544, "relative_start": 0, "end": **********.65317, "relative_end": **********.65317, "duration": 0.5636260509490967, "duration_str": "564ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.6532, "relative_start": 0.****************, "end": **********.713149, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "59.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6ri8WSXdwOuMakMiGYZectcSVF3krr6KkdYK6EIB", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1421505419 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1421505419\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1161385651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1161385651\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1807970485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1807970485\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1976105029 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976105029\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1155658510 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1155658510\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1389576819 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:23:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkwyRk56SnhmOUFzcm5GMVpnSUU5aFE9PSIsInZhbHVlIjoiZFVXalVCejNXRWhhWmNZV1ZrVGdIMEttSUZDOUVoZ2YxbWZyUnZrYnBnM1l3bzV2NWVMMDJtQUNJRXZySXluYldQenRZVTdZM082eEpSK2xhSExIaE1FSytGQUFDNVVnREF4Sm5vdk01SDM5QU0rUGRxZm03d3g1Qldhd1RLU1FwNzNPb0tiWFloclJ2RUNJUjVyeWxFTGF6N3FTVDBJQUpVSDRNNW9CeU5objJGRUxXUXNBVlpwR1czMjdSOTlYRmQyRVdjbHM3K1k5emdUMDRBaTlOTGxaRTJiV0xWOENOSXkzS29nMWg4SGpYZSsvMkRmUHZDVDR5M084WG1qOElPbGs5WUNNU3FKdzNDbEVJTHdWZVBrWWFLTmcyNXp4SHFrd3F1RG1kUjlVZ0dsT0pGNi9iemNRdE9Bc2FGMi9QRUQzSjIrWGJvSjZZSktmY0NNK3lDNGtIM25nVHpOU3RjU3kzQkZEeGwvWGVGd1lzQWZLNW51YlJ0ZWxLelhORS80Mk04UEFRQ2ZiL21DYnRLOHJLcEVBQ296S1ZwNVJKWHZVNmpLZVdyQS9iMEdwMjFmSDYvQ1dvSTNFTE0xQUtwU2xncWlMN3plbldPaGltbDJhbFQzK3VMaEZKSVJCSnloZUJLSnRMN3RiSlBLZ0h4WEZxQUlyd1JOUk9NcWMiLCJtYWMiOiI4YTRlNzRlNWY4YWQ5MWUzMDY3ODA3NGVmZjUwNmY3MjVhYjE5ZTc2ZTAzZGQ2MmM3YzE5YWM2YzJmZjczYThjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:23:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Imt6V1pCNWVUTlBGS3p2Y3puL0xxTlE9PSIsInZhbHVlIjoiemhVUWtBbk5wWXNuTDFjMnQ0M0NDVGVtcEdVdmFieWFHazhqVFJ6Q1RkdFVJSnkxaXZWeTVuWnd5WENwZkUxYU10YlRSek5DSjVKaDIrOGVnVk1xV3ZmRmlhR1BIQ2VYZXJhZEpZeVRkZ0I5YmNBTVVsaDMxTS80LzhzZGZCNi9CT1FkYXVoNEovYnE2S2Z2OEo1QVB4YUlUcFM5dVFZaythYWtSS2NSS3AxYW5JeVNpcnkxRDR5OWI3ZFRxb2l1Rm9peWNhZ01RSnN6S2VyeUdOdmE0OU9ldEFoVUY2K2FCYWRsUDF6RGY1dVhiWWFmdTcvYmIyNnJJbGR1VVZXbU84L0t3dFpwc3U1RmNIbEZVWXJYeXYvamhNTmg0Y2VQcUtSbnIzUG90Qmg1NlpDUlVEc0RZWE8rU08xdTZGNFFBTmQzSnJIKzVxcUV5R0NYV3lacTlXVFB3Z29Hb1I4cGpwc3VjckQ4dzg2endWYVNycHd5M0ZtbEx5MWhWMEhiSjdUanZQbFU4NXo4bjNKaGd0emt1QmxhTTNTTytuRCt6L05abVdqWDIyaEdLdUFhMWtJWmhESEVPWUdwdGQzU09STXN6Q1FrNlptSmNxYTRhckxWMDlCTWhTYkg2YjlCdE55RGNvY2hpNlNhRXlrd3UyL3lkWGR5TjN2ZjV4aTQiLCJtYWMiOiI3ODM4OWEwODgzMTdjN2YzN2U5Y2U3Nzg4NTVhNDA3YTNlMjNhNTJjZjZkNTUxN2NiYTZlMGY0NTc2MDlmMGQwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:23:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkwyRk56SnhmOUFzcm5GMVpnSUU5aFE9PSIsInZhbHVlIjoiZFVXalVCejNXRWhhWmNZV1ZrVGdIMEttSUZDOUVoZ2YxbWZyUnZrYnBnM1l3bzV2NWVMMDJtQUNJRXZySXluYldQenRZVTdZM082eEpSK2xhSExIaE1FSytGQUFDNVVnREF4Sm5vdk01SDM5QU0rUGRxZm03d3g1Qldhd1RLU1FwNzNPb0tiWFloclJ2RUNJUjVyeWxFTGF6N3FTVDBJQUpVSDRNNW9CeU5objJGRUxXUXNBVlpwR1czMjdSOTlYRmQyRVdjbHM3K1k5emdUMDRBaTlOTGxaRTJiV0xWOENOSXkzS29nMWg4SGpYZSsvMkRmUHZDVDR5M084WG1qOElPbGs5WUNNU3FKdzNDbEVJTHdWZVBrWWFLTmcyNXp4SHFrd3F1RG1kUjlVZ0dsT0pGNi9iemNRdE9Bc2FGMi9QRUQzSjIrWGJvSjZZSktmY0NNK3lDNGtIM25nVHpOU3RjU3kzQkZEeGwvWGVGd1lzQWZLNW51YlJ0ZWxLelhORS80Mk04UEFRQ2ZiL21DYnRLOHJLcEVBQ296S1ZwNVJKWHZVNmpLZVdyQS9iMEdwMjFmSDYvQ1dvSTNFTE0xQUtwU2xncWlMN3plbldPaGltbDJhbFQzK3VMaEZKSVJCSnloZUJLSnRMN3RiSlBLZ0h4WEZxQUlyd1JOUk9NcWMiLCJtYWMiOiI4YTRlNzRlNWY4YWQ5MWUzMDY3ODA3NGVmZjUwNmY3MjVhYjE5ZTc2ZTAzZGQ2MmM3YzE5YWM2YzJmZjczYThjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:23:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Imt6V1pCNWVUTlBGS3p2Y3puL0xxTlE9PSIsInZhbHVlIjoiemhVUWtBbk5wWXNuTDFjMnQ0M0NDVGVtcEdVdmFieWFHazhqVFJ6Q1RkdFVJSnkxaXZWeTVuWnd5WENwZkUxYU10YlRSek5DSjVKaDIrOGVnVk1xV3ZmRmlhR1BIQ2VYZXJhZEpZeVRkZ0I5YmNBTVVsaDMxTS80LzhzZGZCNi9CT1FkYXVoNEovYnE2S2Z2OEo1QVB4YUlUcFM5dVFZaythYWtSS2NSS3AxYW5JeVNpcnkxRDR5OWI3ZFRxb2l1Rm9peWNhZ01RSnN6S2VyeUdOdmE0OU9ldEFoVUY2K2FCYWRsUDF6RGY1dVhiWWFmdTcvYmIyNnJJbGR1VVZXbU84L0t3dFpwc3U1RmNIbEZVWXJYeXYvamhNTmg0Y2VQcUtSbnIzUG90Qmg1NlpDUlVEc0RZWE8rU08xdTZGNFFBTmQzSnJIKzVxcUV5R0NYV3lacTlXVFB3Z29Hb1I4cGpwc3VjckQ4dzg2endWYVNycHd5M0ZtbEx5MWhWMEhiSjdUanZQbFU4NXo4bjNKaGd0emt1QmxhTTNTTytuRCt6L05abVdqWDIyaEdLdUFhMWtJWmhESEVPWUdwdGQzU09STXN6Q1FrNlptSmNxYTRhckxWMDlCTWhTYkg2YjlCdE55RGNvY2hpNlNhRXlrd3UyL3lkWGR5TjN2ZjV4aTQiLCJtYWMiOiI3ODM4OWEwODgzMTdjN2YzN2U5Y2U3Nzg4NTVhNDA3YTNlMjNhNTJjZjZkNTUxN2NiYTZlMGY0NTc2MDlmMGQwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:23:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389576819\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1002371887 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ri8WSXdwOuMakMiGYZectcSVF3krr6KkdYK6EIB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1002371887\", {\"maxDepth\":0})</script>\n"}}
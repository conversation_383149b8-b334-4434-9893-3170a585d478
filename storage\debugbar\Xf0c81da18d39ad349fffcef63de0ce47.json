{"__meta": {"id": "Xf0c81da18d39ad349fffcef63de0ce47", "datetime": "2025-07-29 17:14:58", "utime": **********.805176, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[17:14:58] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.799285, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753809297.712145, "end": **********.805201, "duration": 1.0930559635162354, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1753809297.712145, "relative_start": 0, "end": **********.603604, "relative_end": **********.603604, "duration": 0.8914589881896973, "duration_str": "891ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.603632, "relative_start": 0.8914868831634521, "end": **********.805203, "relative_end": 1.9073486328125e-06, "duration": 0.20157098770141602, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48332600, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.01053, "accumulated_duration_str": "10.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.676657, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 27.066}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.702692, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 27.066, "width_percent": 5.603}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.706365, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "radhe_same", "start_percent": 32.669, "width_percent": 4.368}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.711492, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 37.037, "width_percent": 4.653}, {"sql": "select * from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7160559, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 41.69, "width_percent": 7.312}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.735179, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 49.003, "width_percent": 7.123}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.743725, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 56.125, "width_percent": 6.458}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.749368, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 62.583, "width_percent": 8.832}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.756003, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 71.415, "width_percent": 28.585}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 542, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 545, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/expense/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1340365828 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1340365828\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1548992755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1548992755\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-597897999 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597897999\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1946386668 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/expense/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2718 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; omx_ai_suite_session=eyJpdiI6Im02aWF1ZnE3RTQvSmtiMW5BcERQV3c9PSIsInZhbHVlIjoiUGE4L1gwR29Ya0tIc2pGYVovTzZId2k2TGFSdnRLV21sK1AyMlNIZVRoOVdjaU1jN0Robk5HZ2doSkpmY0FXemFhUzdMQjUxNmZCY2xtcDF0ODBvbVpNQmJtL05WWGFTQnRrcDF0R09NUG0yV2x0VlhQMk1TSXpWU1lWY1FFSEhuNHlidlN4V1BQVkh2ajNIb3VYZk1LNjRzOXFXc1Y4MU1jQkh3Y0xwYWU3NkxXMmFYZENJSldkbFJzTWJKSE5pQ25oMGIrMGVMNFdjMG45ZEI5b1Z4NjR0M0EvWjBVSHZyVW12TFgvZ2ZuOEVEdlR1Wi9DRUxvWnp2cXlTZk1LWDJqVmcwZExoZThzM0YzNWVUdmV0Z2tmQmc1UFdrMHpkVDhQdWhIMmxVN3J4MGtOOUZqRm1QcG1yK2Z6STcreUcxcEdnLzNDaTNVakcxdlJiVWdGQXlLV3BKejdidXBPM3ZNcU9EQkZWaG9mTkl1Z2Jjb0JwK2wvOUJRYjNYN2ZjYlJCU3ZUSVRhc29CMFdCR3FRY09wQkdML25lYXp6bzFDTG5EWS81TnowdUxVMVpLUUJsUEhxNUJkZDJGZVFWVENkOC9ETytPaHBiSzE2RUdkRkdTTFc0SGZURzlCVjBteEZrSDJPWUtVVmNTQ1RoRGg1TDMvdW8yYkNuNHhPZGsiLCJtYWMiOiIzZDU3ZjdlZDM2MDg5ZDJhMWRkMDMxYjk2ZDhjNGY1YTY3MmE3YmNlYzRjYmM3YmQyZTBkMThhZGFlZDEwYjIwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImpEYVY1SHl4YTNWeWJMdDV1UUhzMGc9PSIsInZhbHVlIjoiTmltTWRRbk9nNWtPdDZZTkNFUEJMdzJYS0wxVUkrQXVScUtVRzkyT0xhNTUvOWxUOWxXSlplUDR4OTE0b0NoWWl5UDUvcjg5ajZtcGxLOW9PYW8xMVhxWTRGUGU1anlWQ2QvZ1ZaTldIMUZVdVBNM095cGhaWm1nYW9IQXk5OWNXYU0xOXg0YnU3QjB6YW1EVmZlU1duTE5QaWI3aHljcitaWC9oa3JQd0QrV0pUUkU4WHZvd25IK093d00ydWdwUE5OL216M1h6TGxKeFBJT3RTeWhUR1NlSmN6QTRaU1hlb0FmS2xFcGxEU1ljMlI4MjQ1V3lYQ1RDckhLSkNlUkxreE1Wd1NrdjRhcGFlTnNsOTJWNDFDS3dZb25jWUVDQ2I2YkNPV3JWTFFXNTVhRzAwaFV0K2lFTmdhcFpZMG5KN0srNWJUTFBsS2k3UjdRNHFPWmF1M2RIU0NlZVBYY2RZTVBXakdhUGd3dEIrTzVoQ1FJSVloeGY4Y0QzNkJSQ2pZU0dDK21pS3VKekVOT0lVWGVISTJ4V2lZTDA1cWU2Y041Q2hSL0dUVUFOZ1Jwbzgzb0VFRDYzRjkzZjFlYWtxU2pITmxhOEQ4MlJtNHRTdEkzandZOFQxSEhaSFJhQWlSdEdoeCtORnZyUUJmT0F4ZDFuNUxxNk8vbmZ6WGIiLCJtYWMiOiI4OTYxN2Y0MmMyYWNkM2QwZTgzNWIyZjM1MTg4ZTg2YmE1Njk1ZjU3ZmM1NGI3NjQ2ZWI5ZWNjNGVjZDM4NTU3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImY0TUUyZmNLelovYmJFdHVHSkhpd3c9PSIsInZhbHVlIjoiRWZIVUJxekZhMUdTbnBqdWU4YXRFUjFnaUdvanJPZUZQdkoxa1BRd1RvbGhlUGN5T2tyV29qdEMvSnB1R0djV3lnUDA3eG91b3NjVXQxMFZiR1FmVFkvejB5VktZMERJa09FcW4wcVJubmY3dTM3eVJISjdXWXhBVzM1Rm5iOTNKSFRGK3VSWmVXRGhONFRNakthRVFLNlVoU0ZtMFlBelRwSk5zNmt5SSt4cGIwVW9ad0F3c29QdzJ1WjROOWhmakxZTGVBZHhtTTQvM0dUem5scFdya054VmllcWtQZHFKN2xHY2NFMVNKeTgrNFpTeXlhRHBZaEVMdnd3L3JnMEpDaWxqRVJVUm1Pdk0zR29TN200c2xTWnAxNVgwZkV0TzlNZkxuUXk5K0lJVURCT3VBM2ttUHNWOHRVM2Y1QlZLVXh5RDJVYjlEYUFiUzdIM0V6Ui9oRG5ZQXdrbjBSV05kd2FQQ2NZT29jNTgzUU93UkhJNWVyd0RZVWttbm9SNUtyQUFnL3RFR3NLdGVBN3hmbmdBZFRqRnFKNXlQZ2NZeGhIcnJjeVRidFlLMjQzTGRnM1YrOXd0bU9hZU5qQTA5QzlZUnk0ZE01dytxTDZEWS9DRExkUlFrTUlML1RzdGI4VTh4cEV5cS9oZS9EcVg2Uy9INTVxazYxVHVmQmMiLCJtYWMiOiJiYTlhZDI0NDRiOTZmNDg1M2I0YTBjNTI3YjgyYWRlMTBmOGJiZmMxZDA5YzE0Zjk1OTJlNmM0YjFlMWUyMDdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946386668\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1611137823 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WnT8J6dr1q3cGe2zznYjRzgdZFNO79ma6VzwnyJd</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MYGtST6gzPoxGtZLL5j826OymweTDicqK5wxV4qK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611137823\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1937935093 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:14:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhJWFBqUm9qNllXUmgwU04vMERJNUE9PSIsInZhbHVlIjoiaTBFNktQdUxhODVqTzJYTjM1UndDb2JUcGFld2xtS29IRytPQlprMlhhekVHMGxDdDI0dzVmQUdOL283czZVUzVtQUpzQ1dEclBrOVM1NUJscWNtOWs2Z0tibjJQSzhXNkQ2K3hHQ3BqaVFYZzNINUl2YWx6T2lpNDNXZjg5WW1aTGMwYjNrQkIvVmFrdFluMGVmVkdMeXpRSDFacTNNaVpOZFppSEVIWFlFdnZNV09oZW55NjNjQzVSL2pKUnZaVGZOdVY5NEtOTzIwNmVZWjFKVE5GWXhaaG9DTlpaYVZ5Sm9iekI4NGJmTmc1c0FWRzFNVTJxWmx1WVp5OGZ6ZHZENGx1cTVCOWJiSktpV3ZwMGJ2NG9TZjgzb0FZQjRmdVFYbmUzaEhpTkJpZk9VYVJ4aXExUHVQQlRqbGk4VzBCbXVNSlFyWmkwaVU0d0dDMVB0U2dXaFhLWFg4N3FCWDlaR2xpMFBrendleXp3cU5VVnJ4OXpyMndUREFudVdPaUlXNUJQcU1wNkMyd0w3ejhoMmNNL2x5R1VDa2E5dWVuQS9sVVFCaDF4cjYwNWY4cktNRlRmcUpzYVMyNjVKV2kwbjltTmQ3MVQyYnJ6TnpsMkhMSFg1cVEvWm4xMUtOdnZvcFVGZzhRcUE5R1hGZ1JqZnNUQWFnTnhQUVBuK2MiLCJtYWMiOiJjZjA5ZGI4ZTgzYzFlOGRiMzM2MTkyNDJjNTA4OGE1NjA1OWM3ZjcwYTgxYTFmMGE1NDliNjJlMGRjN2IyOGE0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:14:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlJmM2ZKWFE1MjN3emgzeEFGckEyOHc9PSIsInZhbHVlIjoiS3g2N0kzUElVOGZOSVRQenJXaitMMFVlbUlhNjNnVWp1S0FZVU5pNVhSRHFBY3VZc0hKblk4djJZMjRzRDUxdGkzLzNETkZuRmxqc1pZbXQ5ZzRUWHIyTDArRGk2dnNOL3BZdDdVN0w3dmM3MWp4ZnRYY2pXem1TWlpCTWZrWjdJMi9GZmptODYwMmREV2dLcXlLVjJ6b1NhZ3I4V2c0WnJqN2xVckdLeVlDRC9TdGFnK3h4REpNSzQ2M0RHZkRwN29WUEhlOG9Cb08ySkIrcENUZDc0cXdUYUxDWUhlS29QSUtaQ0E5OFhtc01nSGJEVyttWmZMdktqOG52Z3Q4VnpBdXY3b3RXZm5Oc3BzSVYxU1lwQ1JvSjZjUWh2c3BENDR0U3ZyNTFOMHBVRmd4WmlCSFM2VnQvZlFtRmVUQ0NLSjc3YURtMFB4Y0owZTBieExSSFdaR0NnUHZJNWpHRmU1cHJnRzhkcVFCbGNxN3JKUnNPNitLRnJXb1p4RjhOWE9Ha25ubEVLU3FRRHhDVHFRRFRrbVYrNFBEU1VFY3QrbFZxRm9GaXdhZ0ExWGdpK2FERDVzc05mczZlNGtGY1dSTENid1d0N09XcENIaUk2N1ZYSGp4bXJHMDhnUEFOaHFvM29xUjhqL1lFclVJdXI0MXlRTGhIUU5POENaTFkiLCJtYWMiOiJlOWM3YTk1NTgzYjIxN2VlNDVjZTU2OGU0ZDc4ZTBmZmVlM2IwZDU0MWQ5ODQ2MmRhZjdiMTk5YTlmYTkzMzM3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:14:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhJWFBqUm9qNllXUmgwU04vMERJNUE9PSIsInZhbHVlIjoiaTBFNktQdUxhODVqTzJYTjM1UndDb2JUcGFld2xtS29IRytPQlprMlhhekVHMGxDdDI0dzVmQUdOL283czZVUzVtQUpzQ1dEclBrOVM1NUJscWNtOWs2Z0tibjJQSzhXNkQ2K3hHQ3BqaVFYZzNINUl2YWx6T2lpNDNXZjg5WW1aTGMwYjNrQkIvVmFrdFluMGVmVkdMeXpRSDFacTNNaVpOZFppSEVIWFlFdnZNV09oZW55NjNjQzVSL2pKUnZaVGZOdVY5NEtOTzIwNmVZWjFKVE5GWXhaaG9DTlpaYVZ5Sm9iekI4NGJmTmc1c0FWRzFNVTJxWmx1WVp5OGZ6ZHZENGx1cTVCOWJiSktpV3ZwMGJ2NG9TZjgzb0FZQjRmdVFYbmUzaEhpTkJpZk9VYVJ4aXExUHVQQlRqbGk4VzBCbXVNSlFyWmkwaVU0d0dDMVB0U2dXaFhLWFg4N3FCWDlaR2xpMFBrendleXp3cU5VVnJ4OXpyMndUREFudVdPaUlXNUJQcU1wNkMyd0w3ejhoMmNNL2x5R1VDa2E5dWVuQS9sVVFCaDF4cjYwNWY4cktNRlRmcUpzYVMyNjVKV2kwbjltTmQ3MVQyYnJ6TnpsMkhMSFg1cVEvWm4xMUtOdnZvcFVGZzhRcUE5R1hGZ1JqZnNUQWFnTnhQUVBuK2MiLCJtYWMiOiJjZjA5ZGI4ZTgzYzFlOGRiMzM2MTkyNDJjNTA4OGE1NjA1OWM3ZjcwYTgxYTFmMGE1NDliNjJlMGRjN2IyOGE0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:14:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlJmM2ZKWFE1MjN3emgzeEFGckEyOHc9PSIsInZhbHVlIjoiS3g2N0kzUElVOGZOSVRQenJXaitMMFVlbUlhNjNnVWp1S0FZVU5pNVhSRHFBY3VZc0hKblk4djJZMjRzRDUxdGkzLzNETkZuRmxqc1pZbXQ5ZzRUWHIyTDArRGk2dnNOL3BZdDdVN0w3dmM3MWp4ZnRYY2pXem1TWlpCTWZrWjdJMi9GZmptODYwMmREV2dLcXlLVjJ6b1NhZ3I4V2c0WnJqN2xVckdLeVlDRC9TdGFnK3h4REpNSzQ2M0RHZkRwN29WUEhlOG9Cb08ySkIrcENUZDc0cXdUYUxDWUhlS29QSUtaQ0E5OFhtc01nSGJEVyttWmZMdktqOG52Z3Q4VnpBdXY3b3RXZm5Oc3BzSVYxU1lwQ1JvSjZjUWh2c3BENDR0U3ZyNTFOMHBVRmd4WmlCSFM2VnQvZlFtRmVUQ0NLSjc3YURtMFB4Y0owZTBieExSSFdaR0NnUHZJNWpHRmU1cHJnRzhkcVFCbGNxN3JKUnNPNitLRnJXb1p4RjhOWE9Ha25ubEVLU3FRRHhDVHFRRFRrbVYrNFBEU1VFY3QrbFZxRm9GaXdhZ0ExWGdpK2FERDVzc05mczZlNGtGY1dSTENid1d0N09XcENIaUk2N1ZYSGp4bXJHMDhnUEFOaHFvM29xUjhqL1lFclVJdXI0MXlRTGhIUU5POENaTFkiLCJtYWMiOiJlOWM3YTk1NTgzYjIxN2VlNDVjZTU2OGU0ZDc4ZTBmZmVlM2IwZDU0MWQ5ODQ2MmRhZjdiMTk5YTlmYTkzMzM3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:14:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937935093\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-115321033 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/expense/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-115321033\", {\"maxDepth\":0})</script>\n"}}
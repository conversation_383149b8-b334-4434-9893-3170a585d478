{"__meta": {"id": "Xcf3d0ce18d5d502c824ce762944843aa", "datetime": "2025-07-29 17:28:46", "utime": 1753810126.104822, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[17:28:46] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753810126.096546, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753810124.625744, "end": 1753810126.104852, "duration": 1.4791078567504883, "duration_str": "1.48s", "measures": [{"label": "Booting", "start": 1753810124.625744, "relative_start": 0, "end": **********.853812, "relative_end": **********.853812, "duration": 1.2280678749084473, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.853851, "relative_start": 1.22810697555542, "end": 1753810126.104855, "relative_end": 3.0994415283203125e-06, "duration": 0.2510039806365967, "duration_str": "251ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48333920, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03593, "accumulated_duration_str": "35.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9135861, "duration": 0.0061200000000000004, "duration_str": "6.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 17.033}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.93483, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 17.033, "width_percent": 2.561}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 61}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.940082, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Utility.php:61", "source": "app/Models/Utility.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=61", "ajax": false, "filename": "Utility.php", "line": "61"}, "connection": "radhe_same", "start_percent": 19.594, "width_percent": 1.948}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 84 or `ch_messages`.`to_id` = 84 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["84", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.945914, "duration": 0.01684, "duration_str": "16.84ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 21.542, "width_percent": 46.869}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 84", "type": "query", "params": [], "bindings": ["client", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 364}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9669662, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:364", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=364", "ajax": false, "filename": "MessagesController.php", "line": "364"}, "connection": "radhe_same", "start_percent": 68.411, "width_percent": 3.813}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.987037, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 72.224, "width_percent": 2.004}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.999576, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 74.228, "width_percent": 3.34}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1753810126.008304, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 77.567, "width_percent": 2.783}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1753810126.01456, "duration": 0.0070599999999999994, "duration_str": "7.06ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 80.351, "width_percent": 19.649}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 542, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 545, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1093395993 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1093395993\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-662378378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-662378378\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-960550298 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-960550298\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1448175367 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2718 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; omx_ai_suite_session=eyJpdiI6Im02aWF1ZnE3RTQvSmtiMW5BcERQV3c9PSIsInZhbHVlIjoiUGE4L1gwR29Ya0tIc2pGYVovTzZId2k2TGFSdnRLV21sK1AyMlNIZVRoOVdjaU1jN0Robk5HZ2doSkpmY0FXemFhUzdMQjUxNmZCY2xtcDF0ODBvbVpNQmJtL05WWGFTQnRrcDF0R09NUG0yV2x0VlhQMk1TSXpWU1lWY1FFSEhuNHlidlN4V1BQVkh2ajNIb3VYZk1LNjRzOXFXc1Y4MU1jQkh3Y0xwYWU3NkxXMmFYZENJSldkbFJzTWJKSE5pQ25oMGIrMGVMNFdjMG45ZEI5b1Z4NjR0M0EvWjBVSHZyVW12TFgvZ2ZuOEVEdlR1Wi9DRUxvWnp2cXlTZk1LWDJqVmcwZExoZThzM0YzNWVUdmV0Z2tmQmc1UFdrMHpkVDhQdWhIMmxVN3J4MGtOOUZqRm1QcG1yK2Z6STcreUcxcEdnLzNDaTNVakcxdlJiVWdGQXlLV3BKejdidXBPM3ZNcU9EQkZWaG9mTkl1Z2Jjb0JwK2wvOUJRYjNYN2ZjYlJCU3ZUSVRhc29CMFdCR3FRY09wQkdML25lYXp6bzFDTG5EWS81TnowdUxVMVpLUUJsUEhxNUJkZDJGZVFWVENkOC9ETytPaHBiSzE2RUdkRkdTTFc0SGZURzlCVjBteEZrSDJPWUtVVmNTQ1RoRGg1TDMvdW8yYkNuNHhPZGsiLCJtYWMiOiIzZDU3ZjdlZDM2MDg5ZDJhMWRkMDMxYjk2ZDhjNGY1YTY3MmE3YmNlYzRjYmM3YmQyZTBkMThhZGFlZDEwYjIwIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJaMjE5aFJjWUdJVHVVelZTb2J0WkE9PSIsInZhbHVlIjoiYW9BYytEWnhBNDcxV1lKZkRYQ1dTYUlyb09LU3VnOC8vMWgrR0ZPSDZBT2lvUFBoTjFyRU9JQm5ZWk8xajMrcFVVeGlBaGVQVDhySjdZYWlNV0grbUw3YVdDZndxVXZ6RGRPcVAyK04xaDBtTUJ5N2NwS2JCYWdjWnU5WXBDSEZHTEZLQTBTNU9QMEFBbjVaK255NGNjTDd2d2U3OHVzejBhaDRHQVlvdzBpZkVvOG0rVHI1b2RJbG1EeGJhOHMxeTFUOFNZaFUxdVpXalFjZGhjVmZXQ21wTXN0SVpKZnFnTVcyQWtMb29MSVNRNDdDSjBFTlJNQ1kzSlVJaWRRQ3laaWNFWDBwN3NHWHVUV3RYdGlXTXp2Ynhud0ZDU2Y4MlBLUWV6bEhHNHo4L09IaTRaQ2ZMaHB5T2Z4WGdxNEgrY0xGUEdlYmNVZThiYVFXUys1dHROeVB6ZUhDTm1wUkdjMzhhNFNnbkNMUnJjdWtPZnFHRURxZ0NQUjRPbjQyMjhHRDFvNUhNTWtZTXprajUyeDRyL0RVeGM2b3BBSWtNWWdYYWJUZ2U0R3RCc1JJbDdFVW5ZNC9RWFpzbDluSkhJTDYyRFpYbVZRckRaaHRjejJzUFd4TWZvckNDYS9Iblg2cXRURlVBU3FsR3crNmNMYk01UVJoS2FtWnpOa24iLCJtYWMiOiI0Y2NlMTAzMTc5NDAyOTg4Y2ViM2ExOTIxOWQ1MDY2YjRhMzNlYTFkZjQ2MjlmMDhmNDVhOTRhMTNhYmI0ZjM4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InBrZm85dFppRElmUjdwOE55V0lHc0E9PSIsInZhbHVlIjoiNm1MTXljRkFIZExDZXhJNUNlNlFVU0tHbjdMb29wS0tQVkNGWnFHZU9PaUNzRjlCdTlON3FWOVdScU84eUljR1FrRTdzOTRXenJCMjFKL3ZYMk80TWNRenNhcVZzczMrRE1vS2puaURmZmhuZitDSVFSYm9YNmJQc2JHWmJLNVB6TEFyVk9DblQrWXpWTHN3eVpCelkvelhjM1NZMnFySGVNV2ZpTWU5Q2o0SFdyZC9YQ1ZzZ1Y4ZmZoK3pkRGJ3cWtYd1hOdEZNYUp5b1NCSWpuS29Vak5RSnFJNVVJem1WQ0VhbklLbk1JWFpyL2hFdGVEcUw5TnFRL3VwenJ0UlJCNlB6SHJIcVFVQ0JwZG5UOWU0Tm9RUGxKMnh6OUNKSzBISHFoSTRFdEJwQ2xKVURRM0p4UEhaQU1yOHA3cE56THg0VlMyR0lJUjl5b1U2Wk95TEM4UUo3Z3JDSkYzakxlcnVaeWNLYmRvTzRUNTFxc1NtTGsvaHByTGNGS0gvZFo1bDF5ZkZJQkVBTGhoM0drK21FTG9DUjVDd3h3MFZzaThMcEJTS2N5dXU0Q2ZYWVUyNG96aEE0WXhVMnhWdWZCMCs2NFNINS9zajU5SWRYN2xOQVI3ZEVVaUxtenRXNk1EU0VtM1hDa21xN2t0RXpvekpYb3BsQXZuQ2o4UWQiLCJtYWMiOiJmMjRlZTY1NDgyM2JhZmE4NzgwYWI1MzJjNzIxMjQ1YzAxNTZhZjRjOWQwNTIyYzhiZWM4YmQxMzk1OTg4YTIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448175367\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1422118267 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WnT8J6dr1q3cGe2zznYjRzgdZFNO79ma6VzwnyJd</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MYGtST6gzPoxGtZLL5j826OymweTDicqK5wxV4qK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1422118267\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-353182227 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:28:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJCaDN6aVgxOEV2aUp6NnpxU0NVVXc9PSIsInZhbHVlIjoiVk5JUTUyenNNV2NoN29JcVJzRjh0aUZsd1kzVDUrNEF0Nmc2VXpQeWtWUU93SnNDRTVlTEd1RXhlVUZGZis0UjJzNWhXVEpiWDVFdWpyVklzWUtoQVI1NFJYVERZWjZ5Umorci9KRStDV1BqcGVBZEh3U2dCTWsvVmVIdGZ0cjZKUk1QQjJHUzJadlR2dXI0U3R4RXhNb1VjSmVqTG92RloxRDd5ZnlHellvbERhSEZqc1lLNzFhTlFFMVF5QnZqZXdFekdXZFo3cStvWXZ2eXA3d05NSjJMeklmWGxJZk9rcnpXTmx2VWs4c0xjWEJxNHhXOG9tVVluWlB0OWRrdGNveDc2bHlxbENtVmtqTHU1a0lhcUhWNjhOZG4wRzU0SmtaVHpadHk3elhRbTM5S010ZTF4TUQ1OHBZU2ZOZE5Ud1hzWmtLQm5sblluY3ZpNnp1cUZJUGlwaFkyYUxwS05laFdobnUyLzZ0cXNkTDRyM2Nqc2JSVTBFSG5Wait3SjdZSkNkRW54YUtOM3ZQS0Y5VHg2NlJYekMzbW5DcHhKUXQrc2taZUJXc1VFc1ptYW5WMzRXMFFwcS82eklpVjdlNExCR3JSOGRMaVhkb3NYWlRYSzVQdjBySENqRUhVZWxyRFNISERrc2ZqTS9UdHRJaVJ2Nk0wRWZOcU1xNVUiLCJtYWMiOiJmYjU1ZWU2Yjk2YjE2MWE5ZTFmNzQ0YjljNzU5MGZkMWI0NDZlZTUxOWM5NDM4NjkyZDIzMzJmNTRiMGMyZGYzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:28:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlpzUGRscUJleGtQZUxDMWM4L3d4Ync9PSIsInZhbHVlIjoic1BnUDB0cmhNNG5kOElZZVlKd3dtMjdkYnRicWM0Yzc1MVc4SlJpNHRZOTUyWVAwUWdWcERHOWxycXFPaDdUZjdVY1hyUEpPK1dLRUtqemZTdEpqSmEzbElSZFJSQXJoSGVabmJVR3dNUDA5dFNVeHhQNk5NVXhpbFNkQ3pjS2FoQmIzN3RqYUowUlJPYjAzbDViUlZjV0ZJVmdaT2FZclJMMnpvZmlreGROa00zRnVCVTFORWtoRVpnTTAwdm1ndFhJVHFzbnlsN2hDaldqNWZDWVBIdnpsblo2ZU5haVJ4bjQxOU9zYjU2Ym5PWmZlbUs5L0w0QTU4TGIzS3FWMGtGOEI1N2JjRUcyRlltNkVJTEh4UEVCUjBqbjBBaWtLRWQxd0hnYW1Ja2EzeWIwblFjZ0tSakxYTWpNaWl1ZWxBays1eTZ1N0h4ajJaR3lVeVFHU2ViVEkxcjNhN0pGT1lLOXp6ZVkwek9PVy94SXBTc3pwei9KLytOZStGV1hwZkhsU1RId1JCM1dSVkpaN29DOGVvRTkyb3dwNUF3UWZCYnB2Qi9ENWlXVEZoRVBvaFhvWWtxSm10dk8wNUVxOTVseEFOa1hORjNxNW9CRjM5SXhHQUVEOG9IZGZlR3JVa25OUXphSUUrbitiaVozdFRZb1cxMzBPRFVSMTZzckEiLCJtYWMiOiIzMTYyMmI0ZWQ0MzMwYzVjOTVhOTE3NDk3OTA2MTc3YWMxMWUzNDFmYmQzZTViNzQ0MTFmNmM2M2RmMmRkNTE1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:28:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJCaDN6aVgxOEV2aUp6NnpxU0NVVXc9PSIsInZhbHVlIjoiVk5JUTUyenNNV2NoN29JcVJzRjh0aUZsd1kzVDUrNEF0Nmc2VXpQeWtWUU93SnNDRTVlTEd1RXhlVUZGZis0UjJzNWhXVEpiWDVFdWpyVklzWUtoQVI1NFJYVERZWjZ5Umorci9KRStDV1BqcGVBZEh3U2dCTWsvVmVIdGZ0cjZKUk1QQjJHUzJadlR2dXI0U3R4RXhNb1VjSmVqTG92RloxRDd5ZnlHellvbERhSEZqc1lLNzFhTlFFMVF5QnZqZXdFekdXZFo3cStvWXZ2eXA3d05NSjJMeklmWGxJZk9rcnpXTmx2VWs4c0xjWEJxNHhXOG9tVVluWlB0OWRrdGNveDc2bHlxbENtVmtqTHU1a0lhcUhWNjhOZG4wRzU0SmtaVHpadHk3elhRbTM5S010ZTF4TUQ1OHBZU2ZOZE5Ud1hzWmtLQm5sblluY3ZpNnp1cUZJUGlwaFkyYUxwS05laFdobnUyLzZ0cXNkTDRyM2Nqc2JSVTBFSG5Wait3SjdZSkNkRW54YUtOM3ZQS0Y5VHg2NlJYekMzbW5DcHhKUXQrc2taZUJXc1VFc1ptYW5WMzRXMFFwcS82eklpVjdlNExCR3JSOGRMaVhkb3NYWlRYSzVQdjBySENqRUhVZWxyRFNISERrc2ZqTS9UdHRJaVJ2Nk0wRWZOcU1xNVUiLCJtYWMiOiJmYjU1ZWU2Yjk2YjE2MWE5ZTFmNzQ0YjljNzU5MGZkMWI0NDZlZTUxOWM5NDM4NjkyZDIzMzJmNTRiMGMyZGYzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:28:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlpzUGRscUJleGtQZUxDMWM4L3d4Ync9PSIsInZhbHVlIjoic1BnUDB0cmhNNG5kOElZZVlKd3dtMjdkYnRicWM0Yzc1MVc4SlJpNHRZOTUyWVAwUWdWcERHOWxycXFPaDdUZjdVY1hyUEpPK1dLRUtqemZTdEpqSmEzbElSZFJSQXJoSGVabmJVR3dNUDA5dFNVeHhQNk5NVXhpbFNkQ3pjS2FoQmIzN3RqYUowUlJPYjAzbDViUlZjV0ZJVmdaT2FZclJMMnpvZmlreGROa00zRnVCVTFORWtoRVpnTTAwdm1ndFhJVHFzbnlsN2hDaldqNWZDWVBIdnpsblo2ZU5haVJ4bjQxOU9zYjU2Ym5PWmZlbUs5L0w0QTU4TGIzS3FWMGtGOEI1N2JjRUcyRlltNkVJTEh4UEVCUjBqbjBBaWtLRWQxd0hnYW1Ja2EzeWIwblFjZ0tSakxYTWpNaWl1ZWxBays1eTZ1N0h4ajJaR3lVeVFHU2ViVEkxcjNhN0pGT1lLOXp6ZVkwek9PVy94SXBTc3pwei9KLytOZStGV1hwZkhsU1RId1JCM1dSVkpaN29DOGVvRTkyb3dwNUF3UWZCYnB2Qi9ENWlXVEZoRVBvaFhvWWtxSm10dk8wNUVxOTVseEFOa1hORjNxNW9CRjM5SXhHQUVEOG9IZGZlR3JVa25OUXphSUUrbitiaVozdFRZb1cxMzBPRFVSMTZzckEiLCJtYWMiOiIzMTYyMmI0ZWQ0MzMwYzVjOTVhOTE3NDk3OTA2MTc3YWMxMWUzNDFmYmQzZTViNzQ0MTFmNmM2M2RmMmRkNTE1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:28:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353182227\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1134125744 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EWggEWE6dSqx6Q80g5AY5QoNvJEITEhrHeDMUkDM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134125744\", {\"maxDepth\":0})</script>\n"}}
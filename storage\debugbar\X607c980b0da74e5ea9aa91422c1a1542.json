{"__meta": {"id": "X607c980b0da74e5ea9aa91422c1a1542", "datetime": "2025-07-29 17:20:44", "utime": **********.389268, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753809643.320085, "end": **********.389296, "duration": 1.0692110061645508, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": 1753809643.320085, "relative_start": 0, "end": **********.307588, "relative_end": **********.307588, "duration": 0.9875030517578125, "duration_str": "988ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.307604, "relative_start": 0.****************, "end": **********.389299, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "81.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OfOH6AVQMDNBTCVrHXU9YESyWbJCl4HlcIIL7hg5", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1520242498 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1520242498\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-491817682 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-491817682\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-500847057 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-500847057\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1404893121 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1404893121\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1199348463 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1199348463\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1102070796 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:20:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ4eDZvWnBtUUdxZUN2ZVJHQ0lQc3c9PSIsInZhbHVlIjoiMVRBZEhIbjRiY0FGa3VBRHI5dEhpNVlwQVROUkJyc1ZnUDdoOHQ1d3hiWi8yV0dHekJHSjFOTFlhd2ZwVWhnRnZrWHVqUEUxbFlwYkhpQUI4YlhiUW11MjFTL24ydnFMTDV4YnR4WHZraHkvNjZKaUY1TVB6S1NyYUJtUFBldG4yWFA4a1hhTXB3ZW1ZcUJTWk02ZXBRVDd0U1V6d3ZLUENKVDExWDFMM0hLN3ZXUEtPQVllQWpPS2tVQTNGNzlDTjg3N0FXWlBHVmJSTFlYbllQczVSa0ZFNlVUUE1uV1JHVVdUS2VSUXlkSStrajd0WFo5Q0wweWUySmhBT0tHQzhEaGpsWnZhODc4dWsxRXh1R0xBRG9Kc29qKzRpTHRBZDB5NWR2ZlQyczBKRmtQNm9xRGhHRkJZNFpzNkw0ZzRhWi9Da3ZCdzhJMGg3WEFBMVlBc0poUXRRVDJhZ0NZMVgwVkZMWmo0ZElQTzFJVFZyeUNGL0J5ZW9oUVZaUkJ5V2hXMVRqanB0Tko1R0VUNmdpZzkyaERTRXh1NFNSaXZYaHEvY3RMbmw5Yk9mVnRhc3VpajVxaSt6VE9UNVJwRUZJa1I2cmxyYnNtN1BVM0pXbnZFYnI4bXUzN1JEQkkvR0htZ25NcEhsT0dlRTJpVkhvY1k0T0FGWnYvQ2t5d3UiLCJtYWMiOiIyNjlmNmU4ZTg0YWExMjgxYjkzMTJhMzRiODRlNjRkYmIzZDc3Zjc1MTgwOGMxYTFlMjI4YTYyM2RjZTJlMzdkIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:20:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkhHMzgwdFF2OWlTMFZrbmdvajdpRmc9PSIsInZhbHVlIjoiZ0g5aGV5ZDcwYzFZL3l0dW4vS01vMms0cEs1VnlsQUUwekp1QW9UNm5DT0xkSjhNdnNkK2FkK29xcHVBbW9MZVlTbzFQbW9pdER1Szd5Sk10dDVxdnZyZGIyUDRaRnVqNjhiMllhT2xIRVdoenVEUW5BbkxRcU9GQTJDV01kdk5zTXJtY1U3WEptQTFhRmVNRnl1a0VvSFRBWWZwODdxbkNtOFhibncxWlV5SzMyamo2TktnSDNCL1NZZXk0NU9OV1J6eFJlSW1XeWk1ckRXNUIwYS9FR0NnQmxxN1RFRHdvMXZId0hPMzBFdFl5eW01R1AxcnNXN1FjTStnaUZQZG10bmJzNTZLS2NSNjVzOEpIUmN2QUtrWWxDNXRGblplN3FaNU1WUXFmbVl3WjlnYWllMWtHQkh5T2RTeFlPNzR2bWo2MXNmYVl6MUxqRUhXcXpxQlhiT3VyTExPQTFEYU01a2NZK0wxeENBZFVxVXZUK3JvcFhPOG1WWGpCdDBWRDFLQThxdi9yR0E3VGNMK3ltcngxNzY1cjlNbjZhVXdILzVzd0IyaTMrMWJuZ1pkT3V1ODQrcSswYkpZQnl4MTdaOVVIY2lyMnJPbW1MbW9iYnluYnUyU0FmbDRFVlBmZWorVnV4UHgwK2hTTkxCRGlEQklLRjBqUFh1L1dmSngiLCJtYWMiOiJlOTI3OGMxMzNkMTQ0MzRiNzc2N2ZkNDcwOWRjYmFhYTEyMjAxYTAxM2FhODJhYTQ0N2IwNDEyODg0N2NlN2YzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:20:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ4eDZvWnBtUUdxZUN2ZVJHQ0lQc3c9PSIsInZhbHVlIjoiMVRBZEhIbjRiY0FGa3VBRHI5dEhpNVlwQVROUkJyc1ZnUDdoOHQ1d3hiWi8yV0dHekJHSjFOTFlhd2ZwVWhnRnZrWHVqUEUxbFlwYkhpQUI4YlhiUW11MjFTL24ydnFMTDV4YnR4WHZraHkvNjZKaUY1TVB6S1NyYUJtUFBldG4yWFA4a1hhTXB3ZW1ZcUJTWk02ZXBRVDd0U1V6d3ZLUENKVDExWDFMM0hLN3ZXUEtPQVllQWpPS2tVQTNGNzlDTjg3N0FXWlBHVmJSTFlYbllQczVSa0ZFNlVUUE1uV1JHVVdUS2VSUXlkSStrajd0WFo5Q0wweWUySmhBT0tHQzhEaGpsWnZhODc4dWsxRXh1R0xBRG9Kc29qKzRpTHRBZDB5NWR2ZlQyczBKRmtQNm9xRGhHRkJZNFpzNkw0ZzRhWi9Da3ZCdzhJMGg3WEFBMVlBc0poUXRRVDJhZ0NZMVgwVkZMWmo0ZElQTzFJVFZyeUNGL0J5ZW9oUVZaUkJ5V2hXMVRqanB0Tko1R0VUNmdpZzkyaERTRXh1NFNSaXZYaHEvY3RMbmw5Yk9mVnRhc3VpajVxaSt6VE9UNVJwRUZJa1I2cmxyYnNtN1BVM0pXbnZFYnI4bXUzN1JEQkkvR0htZ25NcEhsT0dlRTJpVkhvY1k0T0FGWnYvQ2t5d3UiLCJtYWMiOiIyNjlmNmU4ZTg0YWExMjgxYjkzMTJhMzRiODRlNjRkYmIzZDc3Zjc1MTgwOGMxYTFlMjI4YTYyM2RjZTJlMzdkIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:20:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkhHMzgwdFF2OWlTMFZrbmdvajdpRmc9PSIsInZhbHVlIjoiZ0g5aGV5ZDcwYzFZL3l0dW4vS01vMms0cEs1VnlsQUUwekp1QW9UNm5DT0xkSjhNdnNkK2FkK29xcHVBbW9MZVlTbzFQbW9pdER1Szd5Sk10dDVxdnZyZGIyUDRaRnVqNjhiMllhT2xIRVdoenVEUW5BbkxRcU9GQTJDV01kdk5zTXJtY1U3WEptQTFhRmVNRnl1a0VvSFRBWWZwODdxbkNtOFhibncxWlV5SzMyamo2TktnSDNCL1NZZXk0NU9OV1J6eFJlSW1XeWk1ckRXNUIwYS9FR0NnQmxxN1RFRHdvMXZId0hPMzBFdFl5eW01R1AxcnNXN1FjTStnaUZQZG10bmJzNTZLS2NSNjVzOEpIUmN2QUtrWWxDNXRGblplN3FaNU1WUXFmbVl3WjlnYWllMWtHQkh5T2RTeFlPNzR2bWo2MXNmYVl6MUxqRUhXcXpxQlhiT3VyTExPQTFEYU01a2NZK0wxeENBZFVxVXZUK3JvcFhPOG1WWGpCdDBWRDFLQThxdi9yR0E3VGNMK3ltcngxNzY1cjlNbjZhVXdILzVzd0IyaTMrMWJuZ1pkT3V1ODQrcSswYkpZQnl4MTdaOVVIY2lyMnJPbW1MbW9iYnluYnUyU0FmbDRFVlBmZWorVnV4UHgwK2hTTkxCRGlEQklLRjBqUFh1L1dmSngiLCJtYWMiOiJlOTI3OGMxMzNkMTQ0MzRiNzc2N2ZkNDcwOWRjYmFhYTEyMjAxYTAxM2FhODJhYTQ0N2IwNDEyODg0N2NlN2YzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:20:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102070796\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1661212665 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OfOH6AVQMDNBTCVrHXU9YESyWbJCl4HlcIIL7hg5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1661212665\", {\"maxDepth\":0})</script>\n"}}
{"__meta": {"id": "X921d38e7e1b36dba68c7912fa4aaf450", "datetime": "2025-07-29 17:14:55", "utime": **********.993796, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753809294.763313, "end": **********.993852, "duration": 1.2305388450622559, "duration_str": "1.23s", "measures": [{"label": "Booting", "start": 1753809294.763313, "relative_start": 0, "end": **********.912359, "relative_end": **********.912359, "duration": 1.1490459442138672, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.91239, "relative_start": 1.****************, "end": **********.993856, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "81.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tLLCdiJt4kxYRSwbuJehHfJxAiLQEHe3WZSs99kh", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-476174121 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-476174121\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-739395617 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-739395617\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-640342790 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-640342790\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1822897986 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822897986\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-73247863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-73247863\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-347646471 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:14:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImgrYWxGQTRsSjlCU1RyMkhSL2czU3c9PSIsInZhbHVlIjoiMmJpRS9rMW5FTWlDRkVhMHFxd1dlNzUwU1M4TDcvRWN3Z3JhV3pHOUcyYWN4Z2tQVm1raXFHa0VKNkphQU5iUzh3T0VzTGRSM1VzREdIaHB5dks0NTlDV0VNeHNRdytwUmxxWGN1SmU2N0Njd0RTTGthZm44L1k5N0lhWVc4MGRTbzh1OUZZdXJycDB2NkNqZFdoSmJGbnVla0F4bFRZa2lFQTI1VjJxaTcvUnViNkNxMkg2UmdkZzBJbkdmcHJPT1FkWm1NbEh2ek1jT1pscUtZSlJpemRvcElTd0NvbGF5TEVtNnBpMCthOGxqNk1oNkw3QVpPT0hucUo4TGg2R2piSGJBK1UxU3J4Y3lPSk9RbXIvU1VMOGozQjZhRkcvUnF6ZTVkOHNTdXpXKzFDZkY3eThqR0c1SHdiYy9ua2ZzNlRJWXp4MEU4TU5lZ3BkRVJGeWMwU1NvblRnNnl4UmU3anFoVStzZ0lKK1NrWis4V0F6eTBPcWZvaXBaVHZWVDZOd0N0RUxQRjFGL3VsR29MMWdwVnU2T2dGNGt0cXlZbTJJVGNUQ2NZeEVjc1p4cVZwOU9Na2VEb20xRWhmVWxKTmZCUjVsQnhBVXFQRjRvM1M5OEc3L2Q3eWtRQjRWaVlqaDhSVTlIL1VpNjhPR3RoSDJKTllyWllMTWN1TjMiLCJtYWMiOiJiZTkzNDUzYTQ5YWY3NTdjMjM3N2VhM2U5ZmQ3ZjM5YzRjNTcwZDg0YjEyOTg0NWI4MjVjZWI2ZDMyYWIzODRjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:14:55 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InRMdkdtVnN3L3BRVldGQ0tVV3RFSEE9PSIsInZhbHVlIjoiOHNubSt2aHdlTVROaS9Qanl1N2VDZVBwNkswOTlaOHdESlBUYTZMUXVybkNDNnI2NGlmc0FZcGNkVkY3UFIwaWxpVGJpVkdKUVVVdFF4Q1dHTFJ5VVA5TFhaN3VQMzVvMnBDYlEybEwxQmpsVHoxOU9lOXpReTdZdW1QUkVMa2pqYjA5OUZaMnlqcHR0QS9ZeFRGOVUzNHE4aFhCalhJVElLOEhHL3F3VVFTaFprK3VSSGg3R2VMYmhGWW5nbG9EV0xORVZjZUdWZFhpbDFzQ29CTWtmVnY1LzZGTzkzbFZXMjdiNFVQSEdnZDN0eVU0RGxRSmRlVFpML0RKRGc0NHdmemF5bTVoWFUxb1h5NWFlOGFxYU4vL1ZJSURDZmlldTdRYnVUTFlOaXlFMEFJWGo3Z3MxNk9JMXRZUVdTaFBKNzdUUlNJUmlTakZBdVhCcmNjb2o5R25mY2ZJbUVyU0FXaDR0a05GMWhUM1RHTElQVXJqVHZ0bldvMXV0ZWg0SG9wdWZvRm5iZzF5NURXUDFYZEN4azY2NXBVMjdzNG1FN0tmSEtsQ3A0VkloWGI1YmtCcFZzWFI2dmxablEyZ1RuWDJiTmhKa0NQWmZ1UjRJMGJRblZZcGluVU9nRGlSL3diRWFubGtpK3htdWxDS2o3QmNFc25ma2wvQzR6Y1EiLCJtYWMiOiJkZGIzNWE5MDg0MzVhMWNiNTkyMWZjZmFkM2QzMDA2MjA5NWZjZGQ2Yjg4NzMwYWNjMzZhOTVmOTZiMjc0MmQzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:14:55 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImgrYWxGQTRsSjlCU1RyMkhSL2czU3c9PSIsInZhbHVlIjoiMmJpRS9rMW5FTWlDRkVhMHFxd1dlNzUwU1M4TDcvRWN3Z3JhV3pHOUcyYWN4Z2tQVm1raXFHa0VKNkphQU5iUzh3T0VzTGRSM1VzREdIaHB5dks0NTlDV0VNeHNRdytwUmxxWGN1SmU2N0Njd0RTTGthZm44L1k5N0lhWVc4MGRTbzh1OUZZdXJycDB2NkNqZFdoSmJGbnVla0F4bFRZa2lFQTI1VjJxaTcvUnViNkNxMkg2UmdkZzBJbkdmcHJPT1FkWm1NbEh2ek1jT1pscUtZSlJpemRvcElTd0NvbGF5TEVtNnBpMCthOGxqNk1oNkw3QVpPT0hucUo4TGg2R2piSGJBK1UxU3J4Y3lPSk9RbXIvU1VMOGozQjZhRkcvUnF6ZTVkOHNTdXpXKzFDZkY3eThqR0c1SHdiYy9ua2ZzNlRJWXp4MEU4TU5lZ3BkRVJGeWMwU1NvblRnNnl4UmU3anFoVStzZ0lKK1NrWis4V0F6eTBPcWZvaXBaVHZWVDZOd0N0RUxQRjFGL3VsR29MMWdwVnU2T2dGNGt0cXlZbTJJVGNUQ2NZeEVjc1p4cVZwOU9Na2VEb20xRWhmVWxKTmZCUjVsQnhBVXFQRjRvM1M5OEc3L2Q3eWtRQjRWaVlqaDhSVTlIL1VpNjhPR3RoSDJKTllyWllMTWN1TjMiLCJtYWMiOiJiZTkzNDUzYTQ5YWY3NTdjMjM3N2VhM2U5ZmQ3ZjM5YzRjNTcwZDg0YjEyOTg0NWI4MjVjZWI2ZDMyYWIzODRjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:14:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InRMdkdtVnN3L3BRVldGQ0tVV3RFSEE9PSIsInZhbHVlIjoiOHNubSt2aHdlTVROaS9Qanl1N2VDZVBwNkswOTlaOHdESlBUYTZMUXVybkNDNnI2NGlmc0FZcGNkVkY3UFIwaWxpVGJpVkdKUVVVdFF4Q1dHTFJ5VVA5TFhaN3VQMzVvMnBDYlEybEwxQmpsVHoxOU9lOXpReTdZdW1QUkVMa2pqYjA5OUZaMnlqcHR0QS9ZeFRGOVUzNHE4aFhCalhJVElLOEhHL3F3VVFTaFprK3VSSGg3R2VMYmhGWW5nbG9EV0xORVZjZUdWZFhpbDFzQ29CTWtmVnY1LzZGTzkzbFZXMjdiNFVQSEdnZDN0eVU0RGxRSmRlVFpML0RKRGc0NHdmemF5bTVoWFUxb1h5NWFlOGFxYU4vL1ZJSURDZmlldTdRYnVUTFlOaXlFMEFJWGo3Z3MxNk9JMXRZUVdTaFBKNzdUUlNJUmlTakZBdVhCcmNjb2o5R25mY2ZJbUVyU0FXaDR0a05GMWhUM1RHTElQVXJqVHZ0bldvMXV0ZWg0SG9wdWZvRm5iZzF5NURXUDFYZEN4azY2NXBVMjdzNG1FN0tmSEtsQ3A0VkloWGI1YmtCcFZzWFI2dmxablEyZ1RuWDJiTmhKa0NQWmZ1UjRJMGJRblZZcGluVU9nRGlSL3diRWFubGtpK3htdWxDS2o3QmNFc25ma2wvQzR6Y1EiLCJtYWMiOiJkZGIzNWE5MDg0MzVhMWNiNTkyMWZjZmFkM2QzMDA2MjA5NWZjZGQ2Yjg4NzMwYWNjMzZhOTVmOTZiMjc0MmQzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:14:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347646471\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1163592728 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tLLCdiJt4kxYRSwbuJehHfJxAiLQEHe3WZSs99kh</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163592728\", {\"maxDepth\":0})</script>\n"}}
{"__meta": {"id": "Xf48313271ce25b5287b5f2813506548f", "datetime": "2025-07-29 17:26:09", "utime": **********.957672, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753809968.943457, "end": **********.957715, "duration": 1.0142581462860107, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": 1753809968.943457, "relative_start": 0, "end": **********.875783, "relative_end": **********.875783, "duration": 0.932326078414917, "duration_str": "932ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.875799, "relative_start": 0.****************, "end": **********.957717, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "81.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "1uoFO8svQTOCYkw2EEWrwuVT2sNRUtJoGblBK7nk", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-546073784 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-546073784\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-365363422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-365363422\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1246061608 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1246061608\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-348660092 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348660092\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1444189865 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1444189865\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1302338074 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:26:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iis2NVJDM3JIdkF1LzBqQzZzMWZ3OVE9PSIsInZhbHVlIjoiK0pSYXlvNis5d3J5ckM4Yld1VDJoUDNjVUpBaVpjVEZ4ODNUSHArSWFLS2NYNzZERjc1MVp4ZjVCS0ZZeFlLMW4yTWFlT0tjWWozWmt1MkZiVS9sVEI5WDlxelVpNy9sM0o4S1hrZW9ySStqU2VaU1gxVWJIYlBMQ2NFN2dWdXAwU2xDUFRIVWhoU2lNR3dBcUlCV0IwTHAySmVDZU5aQzA4V2RWVlg5ZWE3MzB4d0gxQm9XaTVSOTB1NWRvWjFMVlB3cXpKdDVlRFQzbmkwVldWaGVlQXVLeFdMNFVJWlZDYWNxZ3Nrd3FwYU9tdDd5YXlDM3ZYOHdzV25OdHozdFJoRjdTNWgxOUxQU3lyR3lQL1ZuSUduWi9VaG4yYkE2UDdTNTBqWHF0SEM5MDJobDdxUkh5WTlJekFGWUpDWU4yajg3M1lHa2dHYkp6bXlTOGtza3lxeVB3MkFzYnJZMlpHeFNEWXNuSkFCQkl5SVJaOG1Ec21kdE1pVmw3NVJDNXh6cjJIeXFJaTR1UU1CcXZxZnMvbjBrc0F1SFVJcVJYSVpJQnJMNWtWR1ZlbVhsUk5BVjkyWUkvUVRxL2duQUI3YllKVFJWZ21rblkzSUkvemc4UEhDTG1nSDJKdDhIdjhVMWhPYis0d25PYlEzL3hGUmF6R0JmeUU0Y0Y2aDUiLCJtYWMiOiIyZTIxNjE2OGI0YmIyYTBkYjJhMjM1N2NlY2NlYjM0OTA3YmE4NzE2ODM5YThhMWY5MWE0ZDk0Zjc5MzE3YTdlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:26:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InJZTE9Fb1BOQVdCWnR1UFBJR3ZyNGc9PSIsInZhbHVlIjoiT09mSDhPeHkzN1g4K2JNeUV4b2VSRVVBbzFQL3dLdTlxSDZCNXBGMmVjMFVnK0liUHpiZkZCcHdHSlhRMEY3YU5zVHRkMndXWi91cW16c0RZOHhjNnRqNjhxdHlyR1krVzdvemV2Vk95Nk5KbEdJMG90UzdoM25RNm9WcmJuNmk1VTVOclFKYktxWFhkaTN3N1hMSUpvV1d1MEhFQVdrOVlqY1NycjlCM0FQaHhjcWZRUkt3eHl6VVVlNWZWakpOL1d2Q2ZFVUprbVFQdFkxU2huaVE4eSt4RGE1bUlMRmYyaEFkODExYkJDT1RreTgrbTR4U3k3MHFSWi8wWk5UYUE3djVPZ2RQMU5hMDkvZG1JdXcrUmJRTU8zSFhmeWFZaU1aVVpmUVhEWEV3MnFDdUlwOFZrUVh6S3Z6QjlDdUwvZ1NMTmNYT0ZuZFJBSGcrb2ljNVlEV1FGYkU3THJKZWtpRXZwT0ptUnJoWHE0NmdUb3R1U0haVnRKZ2pRNXRCVDQvbFZ3Mytxck9paGNKOHZjYkZ5a1I0U0R0UkNIdGJWVWNML3IydE9pQ0hGKy9lb0J3U2JRU0lNdlFiSkZNZ1huMWtVbEFnK2x6bVppNXJzVjgxNGc5U2dkNGlXVUlKMElUaEhMb1A2Tnl5cjFuRC9XenE4QkZPTzZxVjdITFgiLCJtYWMiOiI1MGJjY2RjY2FjNTM5MGJjNmFmNWNiNGVhY2ZjYmI1Yzk3ZjFhN2JjNjYzNzYwYjY2ODIyMWZjY2QyOGZiYzYwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:26:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iis2NVJDM3JIdkF1LzBqQzZzMWZ3OVE9PSIsInZhbHVlIjoiK0pSYXlvNis5d3J5ckM4Yld1VDJoUDNjVUpBaVpjVEZ4ODNUSHArSWFLS2NYNzZERjc1MVp4ZjVCS0ZZeFlLMW4yTWFlT0tjWWozWmt1MkZiVS9sVEI5WDlxelVpNy9sM0o4S1hrZW9ySStqU2VaU1gxVWJIYlBMQ2NFN2dWdXAwU2xDUFRIVWhoU2lNR3dBcUlCV0IwTHAySmVDZU5aQzA4V2RWVlg5ZWE3MzB4d0gxQm9XaTVSOTB1NWRvWjFMVlB3cXpKdDVlRFQzbmkwVldWaGVlQXVLeFdMNFVJWlZDYWNxZ3Nrd3FwYU9tdDd5YXlDM3ZYOHdzV25OdHozdFJoRjdTNWgxOUxQU3lyR3lQL1ZuSUduWi9VaG4yYkE2UDdTNTBqWHF0SEM5MDJobDdxUkh5WTlJekFGWUpDWU4yajg3M1lHa2dHYkp6bXlTOGtza3lxeVB3MkFzYnJZMlpHeFNEWXNuSkFCQkl5SVJaOG1Ec21kdE1pVmw3NVJDNXh6cjJIeXFJaTR1UU1CcXZxZnMvbjBrc0F1SFVJcVJYSVpJQnJMNWtWR1ZlbVhsUk5BVjkyWUkvUVRxL2duQUI3YllKVFJWZ21rblkzSUkvemc4UEhDTG1nSDJKdDhIdjhVMWhPYis0d25PYlEzL3hGUmF6R0JmeUU0Y0Y2aDUiLCJtYWMiOiIyZTIxNjE2OGI0YmIyYTBkYjJhMjM1N2NlY2NlYjM0OTA3YmE4NzE2ODM5YThhMWY5MWE0ZDk0Zjc5MzE3YTdlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:26:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InJZTE9Fb1BOQVdCWnR1UFBJR3ZyNGc9PSIsInZhbHVlIjoiT09mSDhPeHkzN1g4K2JNeUV4b2VSRVVBbzFQL3dLdTlxSDZCNXBGMmVjMFVnK0liUHpiZkZCcHdHSlhRMEY3YU5zVHRkMndXWi91cW16c0RZOHhjNnRqNjhxdHlyR1krVzdvemV2Vk95Nk5KbEdJMG90UzdoM25RNm9WcmJuNmk1VTVOclFKYktxWFhkaTN3N1hMSUpvV1d1MEhFQVdrOVlqY1NycjlCM0FQaHhjcWZRUkt3eHl6VVVlNWZWakpOL1d2Q2ZFVUprbVFQdFkxU2huaVE4eSt4RGE1bUlMRmYyaEFkODExYkJDT1RreTgrbTR4U3k3MHFSWi8wWk5UYUE3djVPZ2RQMU5hMDkvZG1JdXcrUmJRTU8zSFhmeWFZaU1aVVpmUVhEWEV3MnFDdUlwOFZrUVh6S3Z6QjlDdUwvZ1NMTmNYT0ZuZFJBSGcrb2ljNVlEV1FGYkU3THJKZWtpRXZwT0ptUnJoWHE0NmdUb3R1U0haVnRKZ2pRNXRCVDQvbFZ3Mytxck9paGNKOHZjYkZ5a1I0U0R0UkNIdGJWVWNML3IydE9pQ0hGKy9lb0J3U2JRU0lNdlFiSkZNZ1huMWtVbEFnK2x6bVppNXJzVjgxNGc5U2dkNGlXVUlKMElUaEhMb1A2Tnl5cjFuRC9XenE4QkZPTzZxVjdITFgiLCJtYWMiOiI1MGJjY2RjY2FjNTM5MGJjNmFmNWNiNGVhY2ZjYmI1Yzk3ZjFhN2JjNjYzNzYwYjY2ODIyMWZjY2QyOGZiYzYwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:26:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1302338074\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-8679066 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1uoFO8svQTOCYkw2EEWrwuVT2sNRUtJoGblBK7nk</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8679066\", {\"maxDepth\":0})</script>\n"}}
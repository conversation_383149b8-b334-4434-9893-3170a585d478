<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bills', function (Blueprint $table) {
            // Check if columns don't already exist before adding them
            if (!Schema::hasColumn('bills', 'attachment')) {
                $table->string('attachment')->nullable()->after('created_by');
            }
            if (!Schema::hasColumn('bills', 'description')) {
                $table->text('description')->nullable()->after('attachment');
            }
            if (!Schema::hasColumn('bills', 'reference')) {
                $table->string('reference')->nullable()->after('description');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bills', function (Blueprint $table) {
            // Only drop columns that exist
            $columnsToDrop = [];
            $columnsToCheck = ['attachment', 'description', 'reference'];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('bills', $column)) {
                    $columnsToDrop[] = $column;
                }
            }

            if (!empty($columnsToDrop)) {
                $table->dropColumn($columnsToDrop);
            }
        });
    }
};

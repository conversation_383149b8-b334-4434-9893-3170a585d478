<!-- Expense Management Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><?php echo e(__('Expense Table')); ?></h4>
            <div class="d-flex gap-2 align-items-center">
                <button type="button" class="btn btn-success" id="addExpenseBtn">
                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add Expense')); ?>

                </button>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="expenseDropdown" data-bs-toggle="dropdown">
                        10 <i class="ti ti-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" data-limit="10">10</a></li>
                        <li><a class="dropdown-item" href="#" data-limit="25">25</a></li>
                        <li><a class="dropdown-item" href="#" data-limit="50">50</a></li>
                        <li><a class="dropdown-item" href="#" data-limit="100">100</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Expense Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <!-- Filter Row -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Expense Category')); ?></label>
                        <select class="form-select" id="categoryFilter">
                            <option value=""><?php echo e(__('All')); ?></option>
                            <?php
                                $categories = \App\Models\ProductServiceCategory::where('created_by', \Auth::user()->creatorId())
                                    ->where('type', 'expense')
                                    ->get();
                            ?>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Expense')); ?></label>
                        <input type="text" class="form-control" id="expenseFilter" placeholder="<?php echo e(__('Expense')); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Vendor')); ?></label>
                        <input type="text" class="form-control" id="vendorFilter" placeholder="<?php echo e(__('Vendor')); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Date')); ?></label>
                        <input type="date" class="form-control" id="dateFilter" placeholder="<?php echo e(__('Choose date...')); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Expense Receipt')); ?></label>
                        <div class="text-muted">-</div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Action')); ?></label>
                        <div class="text-muted">-</div>
                    </div>
                </div>

                <!-- Table Header -->
                <div class="row mb-2 border-bottom pb-2">
                    <div class="col-md-2">
                        <strong class="text-muted small">#</strong>
                    </div>
                    <div class="col-md-2">
                        <strong class="text-muted small"><?php echo e(__('Expense Category')); ?></strong>
                    </div>
                    <div class="col-md-2">
                        <strong class="text-muted small"><?php echo e(__('Expense')); ?></strong>
                    </div>
                    <div class="col-md-2">
                        <strong class="text-muted small"><?php echo e(__('Vendor')); ?></strong>
                    </div>
                    <div class="col-md-2">
                        <strong class="text-muted small"><?php echo e(__('Date')); ?></strong>
                    </div>
                    <div class="col-md-1">
                        <strong class="text-muted small"><?php echo e(__('Expense Receipt')); ?></strong>
                    </div>
                    <div class="col-md-1">
                        <strong class="text-muted small"><?php echo e(__('Action')); ?></strong>
                    </div>
                </div>

                <!-- Table Body -->
                <div id="expenseTableBody">
                    <?php
                        $expenses = \App\Models\Bill::where('created_by', \Auth::user()->creatorId())
                            ->where('type', 'Expense')
                            ->with(['category', 'vender'])
                            ->orderBy('created_at', 'desc')
                            ->get();
                    ?>
                    <?php $__empty_1 = true; $__currentLoopData = $expenses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="row py-3 border-bottom expense-row align-items-center"
                         data-category="<?php echo e($expense->category_id ?? ''); ?>"
                         data-expense="<?php echo e($expense->bill_id ? \Auth::user()->expenseNumberFormat($expense->bill_id) : ''); ?>"
                         data-vendor="<?php echo e($expense->vender->name ?? ''); ?>"
                         data-date="<?php echo e($expense->bill_date); ?>">
                        <div class="col-md-2">
                            <span class="text-muted"><?php echo e($index + 1); ?></span>
                        </div>
                        <div class="col-md-2">
                            <span class="badge bg-light text-dark">
                                <?php echo e($expense->category->name ?? __('Uncategorized')); ?>

                            </span>
                        </div>
                        <div class="col-md-2">
                            <div>
                                <strong><?php echo e($expense->bill_id ? \Auth::user()->expenseNumberFormat($expense->bill_id) : '-'); ?></strong>
                                <?php if($expense->description): ?>
                                    <br><small class="text-muted"><?php echo e(Str::limit($expense->description, 30)); ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <span><?php echo e($expense->vender->name ?? '-'); ?></span>
                        </div>
                        <div class="col-md-2">
                            <span><?php echo e(\Auth::user()->dateFormat($expense->bill_date)); ?></span>
                        </div>
                        <div class="col-md-1 text-center">
                            <?php if($expense->attachment): ?>
                                <a href="<?php echo e(asset('storage/' . $expense->attachment)); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="ti ti-file-text"></i>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-1 text-center">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="ti ti-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show bill')): ?>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('expense.show', \Crypt::encrypt($expense->id))); ?>">
                                                <i class="ti ti-eye me-2"></i><?php echo e(__('View')); ?>

                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit bill')): ?>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('expense.edit', \Crypt::encrypt($expense->id))); ?>">
                                                <i class="ti ti-edit me-2"></i><?php echo e(__('Edit')); ?>

                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete bill')): ?>
                                        <li>
                                            <a class="dropdown-item text-danger" href="#" onclick="deleteExpense('<?php echo e($expense->id); ?>')">
                                                <i class="ti ti-trash me-2"></i><?php echo e(__('Delete')); ?>

                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="row py-5" id="noRecordRow">
                        <div class="col-12 text-center">
                            <div class="text-muted">
                                <i class="ti ti-receipt" style="font-size: 3rem; opacity: 0.3;"></i>
                                <p class="mt-2 mb-0"><?php echo e(__('No record found')); ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for Expense Table -->
<style>
.expense-row {
    transition: background-color 0.2s ease;
    border-radius: 4px;
    margin-bottom: 2px;
}

.expense-row:hover {
    background-color: #f8f9fa;
}

.expense-table-header {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.dropdown-menu {
    min-width: 120px;
}

.form-select, .form-control {
    font-size: 0.875rem;
}

#expenseTableBody {
    max-height: 600px;
    overflow-y: auto;
}

.text-muted.small {
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
</style>

<!-- Add Expense Modal -->
<div class="modal fade" id="addExpenseModal" tabindex="-1" aria-labelledby="addExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addExpenseModalLabel"><?php echo e(__('Add New Expense')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addExpenseForm" action="<?php echo e(route('expense.store')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="type" value="vendor">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_category" class="form-label"><?php echo e(__('Expense Category')); ?></label>
                                <select class="form-select" id="expense_category" name="category_id" required>
                                    <option value=""><?php echo e(__('Select Category')); ?></option>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_amount" class="form-label"><?php echo e(__('Amount')); ?></label>
                                <input type="number" class="form-control" id="expense_amount" name="totalAmount" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_vendor" class="form-label"><?php echo e(__('Vendor')); ?></label>
                                <select class="form-select" id="expense_vendor" name="vender_id">
                                    <option value=""><?php echo e(__('Select Vendor')); ?></option>
                                    <?php
                                        $vendors = \App\Models\Vender::where('created_by', \Auth::user()->creatorId())->get();
                                    ?>
                                    <?php $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($vendor->id); ?>"><?php echo e($vendor->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_date" class="form-label"><?php echo e(__('Date')); ?></label>
                                <input type="date" class="form-control" id="expense_date" name="payment_date" value="<?php echo e(date('Y-m-d')); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="account_id" class="form-label"><?php echo e(__('Account')); ?></label>
                                <select class="form-select" id="account_id" name="account_id" required>
                                    <option value=""><?php echo e(__('Select Account')); ?></option>
                                    <?php
                                        $accounts = \App\Models\BankAccount::where('created_by', \Auth::user()->creatorId())->get();
                                    ?>
                                    <?php $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($account->id); ?>"><?php echo e($account->account_name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_receipt" class="form-label"><?php echo e(__('Receipt/Attachment')); ?></label>
                                <input type="file" class="form-control" id="expense_receipt" name="attachment" accept="image/*,application/pdf">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group mb-3">
                                <label for="expense_description" class="form-label"><?php echo e(__('Description')); ?></label>
                                <textarea class="form-control" id="expense_description" name="description" rows="3"></textarea>
                            </div>
                        </div>
                        <!-- Hidden fields for items -->
                        <input type="hidden" name="items[0][chart_account_id]" value="1">
                        <input type="hidden" name="items[0][amount]" id="hidden_amount">
                        <input type="hidden" name="items[0][description]" id="hidden_description">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Add Expense')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add Expense Button
    document.getElementById('addExpenseBtn').addEventListener('click', function() {
        const modal = new bootstrap.Modal(document.getElementById('addExpenseModal'));
        modal.show();
    });

    // Filter functionality
    const categoryFilter = document.getElementById('categoryFilter');
    const expenseFilter = document.getElementById('expenseFilter');
    const vendorFilter = document.getElementById('vendorFilter');
    const dateFilter = document.getElementById('dateFilter');
    const expenseRows = document.querySelectorAll('.expense-row');
    const noRecordRow = document.getElementById('noRecordRow');

    function filterTable() {
        const categoryValue = categoryFilter.value;
        const expenseValue = expenseFilter.value.toLowerCase();
        const vendorValue = vendorFilter.value.toLowerCase();
        const dateValue = dateFilter.value;

        let visibleRows = 0;

        expenseRows.forEach(row => {
            const categoryMatch = !categoryValue || row.dataset.category === categoryValue;
            const expenseMatch = !expenseValue || row.dataset.expense.toLowerCase().includes(expenseValue);
            const vendorMatch = !vendorValue || row.dataset.vendor.toLowerCase().includes(vendorValue);
            const dateMatch = !dateValue || row.dataset.date === dateValue;

            if (categoryMatch && expenseMatch && vendorMatch && dateMatch) {
                row.style.display = 'flex';
                visibleRows++;
            } else {
                row.style.display = 'none';
            }
        });

        // Show/hide no record message
        if (noRecordRow) {
            if (visibleRows === 0 && expenseRows.length > 0) {
                noRecordRow.style.display = 'flex';
                noRecordRow.querySelector('p').textContent = '<?php echo e(__("No matching records found")); ?>';
            } else if (visibleRows === 0) {
                noRecordRow.style.display = 'flex';
                noRecordRow.querySelector('p').textContent = '<?php echo e(__("No record found")); ?>';
            } else {
                noRecordRow.style.display = 'none';
            }
        }
    }

    // Add event listeners for filters
    categoryFilter.addEventListener('change', filterTable);
    expenseFilter.addEventListener('input', filterTable);
    vendorFilter.addEventListener('input', filterTable);
    dateFilter.addEventListener('change', filterTable);

    // Limit dropdown functionality
    document.querySelectorAll('[data-limit]').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const limit = this.dataset.limit;
            document.getElementById('expenseDropdown').innerHTML = limit + ' <i class="ti ti-chevron-down ms-1"></i>';

            // Here you would typically reload the table with the new limit
            // For now, we'll just update the dropdown text
        });
    });

    // Form submission
    document.getElementById('addExpenseForm').addEventListener('submit', function(e) {
        e.preventDefault();

        // Update hidden fields
        const amount = document.getElementById('expense_amount').value;
        const description = document.getElementById('expense_description').value;

        document.getElementById('hidden_amount').value = amount;
        document.getElementById('hidden_description').value = description;

        const formData = new FormData(this);

        // Submit the form normally (not AJAX) to work with existing controller
        this.submit();
    });
});

// Delete expense function
function deleteExpense(expenseId) {
    if (confirm('<?php echo e(__("Are you sure you want to delete this expense?")); ?>')) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/expense/${expenseId}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        form.appendChild(csrfToken);

        // Add method override for DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);

        // Submit the form
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/finance/tabs/expenses.blade.php ENDPATH**/ ?>
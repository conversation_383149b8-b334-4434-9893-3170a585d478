<!-- Expense Management Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><?php echo e(__('Expense Table')); ?></h4>
            <div class="d-flex gap-2 align-items-center">
                <button type="button" class="btn btn-success" id="addExpenseBtn">
                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add Expense')); ?>

                </button>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="expenseDropdown" data-bs-toggle="dropdown">
                        10 <i class="ti ti-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" data-limit="10">10</a></li>
                        <li><a class="dropdown-item" href="#" data-limit="25">25</a></li>
                        <li><a class="dropdown-item" href="#" data-limit="50">50</a></li>
                        <li><a class="dropdown-item" href="#" data-limit="100">100</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Expense Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <!-- Filter Row -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Expense Category')); ?></label>
                        <select class="form-select" id="categoryFilter">
                            <option value=""><?php echo e(__('All')); ?></option>
                            <?php
                                $categories = \App\Models\ProductServiceCategory::where('created_by', \Auth::user()->creatorId())
                                    ->where('type', 'expense')
                                    ->get();
                            ?>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Expense')); ?></label>
                        <input type="text" class="form-control" id="expenseFilter" placeholder="<?php echo e(__('Expense')); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Vendor')); ?></label>
                        <input type="text" class="form-control" id="vendorFilter" placeholder="<?php echo e(__('Vendor')); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Date')); ?></label>
                        <input type="date" class="form-control" id="dateFilter" placeholder="<?php echo e(__('Choose date...')); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Expense Receipt')); ?></label>
                        <div class="text-muted">-</div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label text-muted small"><?php echo e(__('Action')); ?></label>
                        <div class="text-muted">-</div>
                    </div>
                </div>

                <!-- Table Header -->
                <div class="row mb-2 border-bottom pb-2">
                    <div class="col-md-2">
                        <strong class="text-muted small">#</strong>
                    </div>
                    <div class="col-md-2">
                        <strong class="text-muted small"><?php echo e(__('Expense Category')); ?></strong>
                    </div>
                    <div class="col-md-2">
                        <strong class="text-muted small"><?php echo e(__('Expense')); ?></strong>
                    </div>
                    <div class="col-md-2">
                        <strong class="text-muted small"><?php echo e(__('Vendor')); ?></strong>
                    </div>
                    <div class="col-md-2">
                        <strong class="text-muted small"><?php echo e(__('Date')); ?></strong>
                    </div>
                    <div class="col-md-1">
                        <strong class="text-muted small"><?php echo e(__('Expense Receipt')); ?></strong>
                    </div>
                    <div class="col-md-1">
                        <strong class="text-muted small"><?php echo e(__('Action')); ?></strong>
                    </div>
                </div>

                <!-- Table Body -->
                <div id="expenseTableBody">
                    <?php
                        $expenses = \App\Models\Bill::where('created_by', \Auth::user()->creatorId())
                            ->where('type', 'Expense')
                            ->with(['category', 'vender'])
                            ->orderBy('created_at', 'desc')
                            ->get();
                    ?>
                    <?php $__empty_1 = true; $__currentLoopData = $expenses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="row py-3 border-bottom expense-row align-items-center"
                         data-category="<?php echo e($expense->category_id ?? ''); ?>"
                         data-expense="<?php echo e($expense->bill_id ? \Auth::user()->expenseNumberFormat($expense->bill_id) : ''); ?>"
                         data-vendor="<?php echo e($expense->vender->name ?? ''); ?>"
                         data-date="<?php echo e($expense->bill_date); ?>">
                        <div class="col-md-2">
                            <span class="text-muted"><?php echo e($index + 1); ?></span>
                        </div>
                        <div class="col-md-2">
                            <span class="badge bg-light text-dark">
                                <?php echo e($expense->category->name ?? __('Uncategorized')); ?>

                            </span>
                        </div>
                        <div class="col-md-2">
                            <div>
                                <strong><?php echo e($expense->bill_id ? \Auth::user()->expenseNumberFormat($expense->bill_id) : '-'); ?></strong>
                                <?php if($expense->description): ?>
                                    <br><small class="text-muted"><?php echo e(Str::limit($expense->description, 30)); ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <span><?php echo e($expense->vender->name ?? '-'); ?></span>
                        </div>
                        <div class="col-md-2">
                            <span><?php echo e(\Auth::user()->dateFormat($expense->bill_date)); ?></span>
                        </div>
                        <div class="col-md-1 text-center">
                            <?php if($expense->attachment): ?>
                                <a href="<?php echo e(asset('storage/' . $expense->attachment)); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="ti ti-file-text"></i>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-1 text-center">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="ti ti-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show bill')): ?>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('expense.show', \Crypt::encrypt($expense->id))); ?>">
                                                <i class="ti ti-eye me-2"></i><?php echo e(__('View')); ?>

                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit bill')): ?>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('expense.edit', \Crypt::encrypt($expense->id))); ?>">
                                                <i class="ti ti-edit me-2"></i><?php echo e(__('Edit')); ?>

                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete bill')): ?>
                                        <li>
                                            <a class="dropdown-item text-danger" href="#" onclick="deleteExpense('<?php echo e($expense->id); ?>')">
                                                <i class="ti ti-trash me-2"></i><?php echo e(__('Delete')); ?>

                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="row py-5" id="noRecordRow">
                        <div class="col-12 text-center">
                            <div class="text-muted">
                                <i class="ti ti-receipt" style="font-size: 3rem; opacity: 0.3;"></i>
                                <p class="mt-2 mb-0"><?php echo e(__('No record found')); ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for Expense Table -->
<style>
.expense-row {
    transition: background-color 0.2s ease;
    border-radius: 4px;
    margin-bottom: 2px;
}

.expense-row:hover {
    background-color: #f8f9fa;
}

.expense-table-header {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.dropdown-menu {
    min-width: 120px;
}

.form-select, .form-control {
    font-size: 0.875rem;
}

#expenseTableBody {
    max-height: 600px;
    overflow-y: auto;
}

.text-muted.small {
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
</style>

<!-- Add Expense Modal -->
<div class="modal fade" id="addExpenseModal" tabindex="-1" aria-labelledby="addExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addExpenseModalLabel"><?php echo e(__('Add New Expense')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addExpenseForm" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="type" value="vendor">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_category" class="form-label"><?php echo e(__('Expense Category')); ?> <span class="text-danger">*</span></label>
                                <select class="form-select" id="expense_category" name="category_id" required>
                                    <option value=""><?php echo e(__('Select Category')); ?></option>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_amount" class="form-label"><?php echo e(__('Amount')); ?> <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="expense_amount" name="amount" step="0.01" min="0" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_vendor" class="form-label"><?php echo e(__('Vendor')); ?></label>
                                <select class="form-select" id="expense_vendor" name="vender_id">
                                    <option value=""><?php echo e(__('Select Vendor')); ?></option>
                                    <?php
                                        $vendors = \App\Models\Vender::where('created_by', \Auth::user()->creatorId())->get();
                                    ?>
                                    <?php $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($vendor->id); ?>"><?php echo e($vendor->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_date" class="form-label"><?php echo e(__('Date')); ?> <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="expense_date" name="bill_date" value="<?php echo e(date('Y-m-d')); ?>" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_receipt" class="form-label"><?php echo e(__('Receipt/Attachment')); ?></label>
                                <input type="file" class="form-control" id="expense_receipt" name="attachment" accept="image/*,application/pdf,.doc,.docx,.xls,.xlsx">
                                <small class="form-text text-muted"><?php echo e(__('Supported formats: PDF, Images, DOC, XLS (Max: 2MB)')); ?></small>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="expense_reference" class="form-label"><?php echo e(__('Reference')); ?></label>
                                <input type="text" class="form-control" id="expense_reference" name="reference" placeholder="<?php echo e(__('Invoice/Receipt Number')); ?>">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group mb-3">
                                <label for="expense_description" class="form-label"><?php echo e(__('Description')); ?></label>
                                <textarea class="form-control" id="expense_description" name="description" rows="3" placeholder="<?php echo e(__('Enter expense description...')); ?>"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success" id="submitExpenseBtn">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                        <?php echo e(__('Add Expense')); ?>

                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add Expense Button
    document.getElementById('addExpenseBtn').addEventListener('click', function() {
        const modal = new bootstrap.Modal(document.getElementById('addExpenseModal'));
        modal.show();
    });

    // Filter functionality
    const categoryFilter = document.getElementById('categoryFilter');
    const expenseFilter = document.getElementById('expenseFilter');
    const vendorFilter = document.getElementById('vendorFilter');
    const dateFilter = document.getElementById('dateFilter');
    const expenseRows = document.querySelectorAll('.expense-row');
    const noRecordRow = document.getElementById('noRecordRow');

    function filterTable() {
        const categoryValue = categoryFilter.value;
        const expenseValue = expenseFilter.value.toLowerCase();
        const vendorValue = vendorFilter.value.toLowerCase();
        const dateValue = dateFilter.value;

        let visibleRows = 0;

        expenseRows.forEach(row => {
            const categoryMatch = !categoryValue || row.dataset.category === categoryValue;
            const expenseMatch = !expenseValue || row.dataset.expense.toLowerCase().includes(expenseValue);
            const vendorMatch = !vendorValue || row.dataset.vendor.toLowerCase().includes(vendorValue);
            const dateMatch = !dateValue || row.dataset.date === dateValue;

            if (categoryMatch && expenseMatch && vendorMatch && dateMatch) {
                row.style.display = 'flex';
                visibleRows++;
            } else {
                row.style.display = 'none';
            }
        });

        // Show/hide no record message
        if (noRecordRow) {
            if (visibleRows === 0 && expenseRows.length > 0) {
                noRecordRow.style.display = 'flex';
                noRecordRow.querySelector('p').textContent = '<?php echo e(__("No matching records found")); ?>';
            } else if (visibleRows === 0) {
                noRecordRow.style.display = 'flex';
                noRecordRow.querySelector('p').textContent = '<?php echo e(__("No record found")); ?>';
            } else {
                noRecordRow.style.display = 'none';
            }
        }
    }

    // Add event listeners for filters
    categoryFilter.addEventListener('change', filterTable);
    expenseFilter.addEventListener('input', filterTable);
    vendorFilter.addEventListener('input', filterTable);
    dateFilter.addEventListener('change', filterTable);

    // Limit dropdown functionality
    document.querySelectorAll('[data-limit]').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const limit = this.dataset.limit;
            document.getElementById('expenseDropdown').innerHTML = limit + ' <i class="ti ti-chevron-down ms-1"></i>';

            // Here you would typically reload the table with the new limit
            // For now, we'll just update the dropdown text
        });
    });

    // Form submission with AJAX
    document.getElementById('addExpenseForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const form = this;
        const submitBtn = document.getElementById('submitExpenseBtn');
        const spinner = submitBtn.querySelector('.spinner-border');
        const formData = new FormData(form);

        // Clear previous validation errors
        clearValidationErrors();

        // Show loading state
        submitBtn.disabled = true;
        spinner.classList.remove('d-none');

        // Submit via AJAX
        fetch('<?php echo e(route("expense.store")); ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('addExpenseModal'));
                modal.hide();

                // Reset form
                form.reset();

                // Add new expense to table
                addExpenseToTable(data.expense);

                // Show success message
                showNotification('success', data.message || '<?php echo e(__("Expense added successfully!")); ?>');
            } else {
                // Handle validation errors
                if (data.errors) {
                    showValidationErrors(data.errors);
                } else {
                    showNotification('error', data.message || '<?php echo e(__("An error occurred while adding the expense.")); ?>');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('error', '<?php echo e(__("An error occurred while adding the expense.")); ?>');
        })
        .finally(() => {
            // Reset loading state
            submitBtn.disabled = false;
            spinner.classList.add('d-none');
        });
    });
});

// Helper functions
function clearValidationErrors() {
    document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
}

function showValidationErrors(errors) {
    Object.keys(errors).forEach(field => {
        const input = document.querySelector(`[name="${field}"]`);
        if (input) {
            input.classList.add('is-invalid');
            const feedback = input.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.textContent = errors[field][0];
            }
        }
    });
}

function showNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function addExpenseToTable(expense) {
    const tableBody = document.getElementById('expenseTableBody');
    const noRecordRow = document.getElementById('noRecordRow');

    // Hide no record message if it exists
    if (noRecordRow) {
        noRecordRow.style.display = 'none';
    }

    // Get current row count for index
    const currentRows = tableBody.querySelectorAll('.expense-row').length;
    const newIndex = currentRows + 1;

    // Create new row HTML
    const newRow = document.createElement('div');
    newRow.className = 'row py-3 border-bottom expense-row align-items-center';
    newRow.setAttribute('data-category', expense.category_id || '');
    newRow.setAttribute('data-expense', expense.bill_number || '');
    newRow.setAttribute('data-vendor', expense.vendor_name || '');
    newRow.setAttribute('data-date', expense.bill_date);

    newRow.innerHTML = `
        <div class="col-md-2">
            <span class="text-muted">${newIndex}</span>
        </div>
        <div class="col-md-2">
            <span class="badge bg-light text-dark">
                ${expense.category_name || '<?php echo e(__("Uncategorized")); ?>'}
            </span>
        </div>
        <div class="col-md-2">
            <div>
                <strong>${expense.bill_number || '-'}</strong>
                ${expense.description ? `<br><small class="text-muted">${expense.description.substring(0, 30)}${expense.description.length > 30 ? '...' : ''}</small>` : ''}
            </div>
        </div>
        <div class="col-md-2">
            <span>${expense.vendor_name || '-'}</span>
        </div>
        <div class="col-md-2">
            <span>${expense.formatted_date}</span>
        </div>
        <div class="col-md-1 text-center">
            ${expense.attachment ?
                `<a href="${expense.attachment_url}" target="_blank" class="btn btn-sm btn-outline-primary">
                    <i class="ti ti-file-text"></i>
                </a>` :
                '<span class="text-muted">-</span>'
            }
        </div>
        <div class="col-md-1 text-center">
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="ti ti-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item" href="${expense.view_url}">
                            <i class="ti ti-eye me-2"></i><?php echo e(__('View')); ?>

                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="${expense.edit_url}">
                            <i class="ti ti-edit me-2"></i><?php echo e(__('Edit')); ?>

                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item text-danger" href="#" onclick="deleteExpense('${expense.id}')">
                            <i class="ti ti-trash me-2"></i><?php echo e(__('Delete')); ?>

                        </a>
                    </li>
                </ul>
            </div>
        </div>
    `;

    // Insert at the beginning of the table body
    tableBody.insertBefore(newRow, tableBody.firstChild);

    // Update row numbers
    updateRowNumbers();
}

function updateRowNumbers() {
    const rows = document.querySelectorAll('.expense-row');
    rows.forEach((row, index) => {
        const numberCell = row.querySelector('.col-md-2:first-child span');
        if (numberCell) {
            numberCell.textContent = index + 1;
        }
    });
}

// Delete expense function
function deleteExpense(expenseId) {
    if (confirm('<?php echo e(__("Are you sure you want to delete this expense?")); ?>')) {
        fetch(`/expense/${expenseId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove the row from table
                const row = document.querySelector(`[onclick="deleteExpense('${expenseId}')"]`).closest('.expense-row');
                if (row) {
                    row.remove();
                    updateRowNumbers();

                    // Show no record message if no rows left
                    const remainingRows = document.querySelectorAll('.expense-row').length;
                    if (remainingRows === 0) {
                        const noRecordRow = document.getElementById('noRecordRow');
                        if (noRecordRow) {
                            noRecordRow.style.display = 'flex';
                        }
                    }
                }
                showNotification('success', data.message || '<?php echo e(__("Expense deleted successfully!")); ?>');
            } else {
                showNotification('error', data.message || '<?php echo e(__("Failed to delete expense.")); ?>');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('error', '<?php echo e(__("An error occurred while deleting the expense.")); ?>');
        });
    }
}
</script>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/finance/tabs/expenses.blade.php ENDPATH**/ ?>
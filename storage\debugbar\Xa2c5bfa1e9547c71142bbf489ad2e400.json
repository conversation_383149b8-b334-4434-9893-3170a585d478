{"__meta": {"id": "Xa2c5bfa1e9547c71142bbf489ad2e400", "datetime": "2025-07-29 17:52:49", "utime": **********.363207, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753811568.471243, "end": **********.363233, "duration": 0.8919901847839355, "duration_str": "892ms", "measures": [{"label": "Booting", "start": 1753811568.471243, "relative_start": 0, "end": **********.277821, "relative_end": **********.277821, "duration": 0.8065781593322754, "duration_str": "807ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.27784, "relative_start": 0.****************, "end": **********.363235, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "85.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "o8Yce0SCdzYXL9X0oC47D3TylO15PuqqNuclrxxJ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1112544304 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1112544304\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-398581905 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-398581905\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1004678629 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1004678629\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1368405037 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1368405037\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1548472867 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1548472867\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2079871739 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:52:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRMUy9LRHIvSG5PUzZxblVlVTh2RkE9PSIsInZhbHVlIjoiMGNtR0RMZHRSVFJISHVZTjJSS1RjUnpVL1ptRFFCbnBEU1QwSWJDUlVsSGZuZERaeFF2QWROY1Q3VGI2WVpBa1FMMVFyWXRJRU9jaUxlcFR4eEoyMHJGalljdDR5RkNqZXFIaE5rTjVzbHJoaHFWQ1lpNVdKY0NOcm0zd0hHM0d6RkMwNGxUNmZ5VzdIWEE5M1U2T3dhNFZySU52SjBTUXVodW9VcWxFNzk3cUViRjlnVmhBSTN2VDVjYXVNaTFDb0JvYlRhT3p5MjluRjZydEZ4VEF3Z04yYjBOUzBwY3I1ZzV4TjdtSkxZdjkrUnhwZVplUGo0a3RVSmZKd3pSemdNYTVBeVhXTUhhVHFSRWRmQ0RFaEJ6cU82b0ZvNlNLdVkyeTdhU01vKzVrbWY2aWwxRFliclRwRGc0ZXVmVFp6cWlNdlVKMndleUd1UFFubDRDdFdUUjE4Vzhoa3JFdHV5M096Mk5kNnVmQ2JwbzNGYitnMmFzL0J1OW95TjJNR2k3QWNyNkltRTVDcTVJaWhkVlRLYnNaWFJQVWhQVWtyaU9mNm9OYUVFTFdEOGNuYUI1cXVvb0c0WDMyTE82bDZkL1lUMkJVajBMRUgyNzZQUW85UFNyWTJOY3B3Mk9mMjJaQTB0bDdKSVhNUVU2NnkyQzN1VkJLYjRxVlo3SjMiLCJtYWMiOiI3NjM3NGFkMWQzODQ5M2E2NTlmYTJlZGYwZmYxNDA3ZDQxZTBkNTdkYTkwYWM4NWZjZWExOTNmMDg2MDExOWFlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:52:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IllYdmhVdzl5RHhiQ0h0WmQya0pvN3c9PSIsInZhbHVlIjoiSmNHNFBvYzBudEQ0SmJ1enY0dzJLbGV1cFZaaXpSdHA3ZEpEcGZnMFJ5eU1lWUxsajFwZUFzWndtZUdQZmtzbkdjcTFRU1hTdXROclFxSXR4UVJBVHBDV0lNVmVVa2xJM1JLcG5TSDhEOXdZY1RDMm1ZTFJqNlIyYVFzWHlXS3I1TXhCb3hycVVZMnd3ZHpldzQvbmFuNi9pYWpiM2k4YWh3cnRIc3oyS2xDc0NJWnRFSVJremQ3eXZJenNmc1hnd2hBcVIyczg3Q1BsMER2T1ZBQUQ0N3pqdy9iWklNSEtTd3dBV2kzUi8wcnUrZ3Bpa2g1RFpEaG91aFN4c1NxdUY5emJTTFg1SGtxUUJrZnVPcEY5ejVKL1hodGNIVFoySG40dk9aZXcwVW03L3JNYW53R2lqdTJyTnFBSmp4a1ZuK2R1RVpPaEpLZXJ4VjBsZVVMNi80aUppU3VQVUlUbTZGc1hsSjhOSHpnRG5LWko3RjdaR2xsYXRXa1c0bDFqQXVUc2NDazZYc2dJMTZCT0FkRmpheGh0QUJqdStvZm04a3VQTHBhclRPNVpZWjZTT3BYVWZvanVkaFlldXZ4aHlaLzNTL1dFZzFMNXM3TFRscFdtV0gyeG1RM2lnbWNrMjBuWXE1RjRycy8vamg5clJINHY3MmRTczBWenAxaTYiLCJtYWMiOiI4NDA0MjQyYTZjYjFlYmZlYmQ2MDZjMzBjNTc1M2QwNzAzZDQxMmViNzQ0MTY2YmZjYmRiYTIwZjZlMTJiM2E0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:52:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRMUy9LRHIvSG5PUzZxblVlVTh2RkE9PSIsInZhbHVlIjoiMGNtR0RMZHRSVFJISHVZTjJSS1RjUnpVL1ptRFFCbnBEU1QwSWJDUlVsSGZuZERaeFF2QWROY1Q3VGI2WVpBa1FMMVFyWXRJRU9jaUxlcFR4eEoyMHJGalljdDR5RkNqZXFIaE5rTjVzbHJoaHFWQ1lpNVdKY0NOcm0zd0hHM0d6RkMwNGxUNmZ5VzdIWEE5M1U2T3dhNFZySU52SjBTUXVodW9VcWxFNzk3cUViRjlnVmhBSTN2VDVjYXVNaTFDb0JvYlRhT3p5MjluRjZydEZ4VEF3Z04yYjBOUzBwY3I1ZzV4TjdtSkxZdjkrUnhwZVplUGo0a3RVSmZKd3pSemdNYTVBeVhXTUhhVHFSRWRmQ0RFaEJ6cU82b0ZvNlNLdVkyeTdhU01vKzVrbWY2aWwxRFliclRwRGc0ZXVmVFp6cWlNdlVKMndleUd1UFFubDRDdFdUUjE4Vzhoa3JFdHV5M096Mk5kNnVmQ2JwbzNGYitnMmFzL0J1OW95TjJNR2k3QWNyNkltRTVDcTVJaWhkVlRLYnNaWFJQVWhQVWtyaU9mNm9OYUVFTFdEOGNuYUI1cXVvb0c0WDMyTE82bDZkL1lUMkJVajBMRUgyNzZQUW85UFNyWTJOY3B3Mk9mMjJaQTB0bDdKSVhNUVU2NnkyQzN1VkJLYjRxVlo3SjMiLCJtYWMiOiI3NjM3NGFkMWQzODQ5M2E2NTlmYTJlZGYwZmYxNDA3ZDQxZTBkNTdkYTkwYWM4NWZjZWExOTNmMDg2MDExOWFlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:52:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IllYdmhVdzl5RHhiQ0h0WmQya0pvN3c9PSIsInZhbHVlIjoiSmNHNFBvYzBudEQ0SmJ1enY0dzJLbGV1cFZaaXpSdHA3ZEpEcGZnMFJ5eU1lWUxsajFwZUFzWndtZUdQZmtzbkdjcTFRU1hTdXROclFxSXR4UVJBVHBDV0lNVmVVa2xJM1JLcG5TSDhEOXdZY1RDMm1ZTFJqNlIyYVFzWHlXS3I1TXhCb3hycVVZMnd3ZHpldzQvbmFuNi9pYWpiM2k4YWh3cnRIc3oyS2xDc0NJWnRFSVJremQ3eXZJenNmc1hnd2hBcVIyczg3Q1BsMER2T1ZBQUQ0N3pqdy9iWklNSEtTd3dBV2kzUi8wcnUrZ3Bpa2g1RFpEaG91aFN4c1NxdUY5emJTTFg1SGtxUUJrZnVPcEY5ejVKL1hodGNIVFoySG40dk9aZXcwVW03L3JNYW53R2lqdTJyTnFBSmp4a1ZuK2R1RVpPaEpLZXJ4VjBsZVVMNi80aUppU3VQVUlUbTZGc1hsSjhOSHpnRG5LWko3RjdaR2xsYXRXa1c0bDFqQXVUc2NDazZYc2dJMTZCT0FkRmpheGh0QUJqdStvZm04a3VQTHBhclRPNVpZWjZTT3BYVWZvanVkaFlldXZ4aHlaLzNTL1dFZzFMNXM3TFRscFdtV0gyeG1RM2lnbWNrMjBuWXE1RjRycy8vamg5clJINHY3MmRTczBWenAxaTYiLCJtYWMiOiI4NDA0MjQyYTZjYjFlYmZlYmQ2MDZjMzBjNTc1M2QwNzAzZDQxMmViNzQ0MTY2YmZjYmRiYTIwZjZlMTJiM2E0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:52:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2079871739\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1731077790 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">o8Yce0SCdzYXL9X0oC47D3TylO15PuqqNuclrxxJ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731077790\", {\"maxDepth\":0})</script>\n"}}
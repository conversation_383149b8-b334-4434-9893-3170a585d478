{"__meta": {"id": "Xc0dd4d5fbceed8734bd3ad05157037af", "datetime": "2025-07-29 17:34:33", "utime": **********.580635, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753810472.556631, "end": **********.580664, "duration": 1.0240328311920166, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1753810472.556631, "relative_start": 0, "end": **********.491472, "relative_end": **********.491472, "duration": 0.9348409175872803, "duration_str": "935ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.491489, "relative_start": 0.****************, "end": **********.580667, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "89.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "mcfCnapUuTHYoKdSxiJAUL0QamaqkhFflrxQ9Mt9", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1450092578 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1450092578\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-50018427 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-50018427\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1321192903 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1321192903\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1267250274 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267250274\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1255459021 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1255459021\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1912266346 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:34:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhUSEZvS0tVTGdYZWtUWThzcVRzQWc9PSIsInZhbHVlIjoiM2c2NzJpZU9PdzlKdnZOWFFyOWN4NkxlazJnZWtpZ3U0bkdUT1Y4QjExQ0xha0RtRHR3ZVhSdDRIL01rSGNUelNwSDNHRU1yV2tOalRLN0YwUkl6ZG96T0dHd2pEZy9vVnFmVE5iaGUyTEFJYmlBQkppREl1NzRwTXNjdzNMUnVKVzRqcC9wcS9rNmJ2SGI3YU80WTRLSWJDUDJjd2tjTGNtU1RMTVViS3RoZHF6MmN1TG0wV3dFRHJqb2VOQ0p2RzlkQTdNTy9oSDk4VlN6alE0TFFxN3p3OVVKVHNLS2NOK0J5bDB0QS9hZ1Z2TCt4WmUwcDZZaU94ZzVkcWN0S0gyam1PR1IwZzkreFdXTjViOXMrYzQ4UHFYMDZnUnBwbnh3YUVjNTZwcXYzTkk5TE5PWnBOd3RoK2llaGpiRUg3ckR4MG1XUFFBaCtUQlU4V2o4TitOcXZVM01PSXB6V242MTFCQ0VBb3JrdFI4SWtGbEl3SGtqb2ZZbklqVWpCdHdNN3J0VDc5cm1Ud1RMSWxWZDFUSUtYN1hYdUNqTzNqNDhQbTlmdTJSR2gwK3F6VnEzNStaWVlvYllSc29jMjAvZzNlcGdUVFgzaDlnbGdzZWxRMWR5N0VVWEpjaVBxZ0d3Mk4yNmc0U0IrR25aUzdmTnBqYURySTdtK3dTSjEiLCJtYWMiOiJhN2E0Yzg4ZjY1ZGI0ODNkYzg1ZDYxMjdlNTY3MjU2NTg0YTc5OWNhZDMxNjYxMzVhNjUzMjFhMDNmNjI3ZTljIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:34:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikt4WFd5WUN6bUxtUXRycEtnaE5oemc9PSIsInZhbHVlIjoiOVhjcTdsdEJnQkV5MW5va0U4VHZVYnBnY25TQTdqellGdC96QU5FWWhXQlVJeW1OL2Q3UmVMNHVCWHhGVWFwREw2TndxaWV6ZHNXV0FJakpLMFc2bUZ1cWRTckJMUzI2S1A4dWZubFpIWjFTbHZ3NDBxT1BlbGJQSHZ6ZnYyejA5cHpqNkU5MUZNNG1KVHY4Qzl6dVBpVWFtNjRreXVHaktwcTJHREhhZUk2aWJ2SnhpRkJ0NTR2K3U4UkxoRk5adjhEakNOOXpzZElteHlzd0NWdHAwbjZNV0NtVU56SE1CdWNwTlhsamlSc0tuWm1GNC9QSWwweTJSU1M0M0hSZmM1ZXJyd0o2QXBySzNmUm9OdDJsM2J1d2pZV0szYnQ3ZUxYRWttTzhiMVY3L3JocmE3b1lXRHdpVEdySm1oS2JSME95TkRRcnhBcHByQVB0WllyK0U2dGE5ZXlWd0ZveWpkSnF0Wk96Vlh5Rk5FVVg0UW0xVlcvNTVDTzFDUDF2RTVZT2d3TkdXT3QxQzlvOWJQNkxrbmlCeDhmU1Ria2pHU3llSkorNmVyWFNGYU5qd1h6dW5PcWpBVDdQREJwWTB5NXdQUnpQV2tXaHgyZHNPVVVRUVY4UGxET2tnQnQzZm1xZDNRUE44WkpySDF3MVZxalkxY1VGZHd1cGlWODMiLCJtYWMiOiIyY2NkYTA0YjI1OTBiM2I1MGUxMmE5ODYyZDFiMzM0ZThjMjQ0OGZlOGY3YjhmZjAwNGMxOWU1NWE0ZGM0ZDFlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:34:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhUSEZvS0tVTGdYZWtUWThzcVRzQWc9PSIsInZhbHVlIjoiM2c2NzJpZU9PdzlKdnZOWFFyOWN4NkxlazJnZWtpZ3U0bkdUT1Y4QjExQ0xha0RtRHR3ZVhSdDRIL01rSGNUelNwSDNHRU1yV2tOalRLN0YwUkl6ZG96T0dHd2pEZy9vVnFmVE5iaGUyTEFJYmlBQkppREl1NzRwTXNjdzNMUnVKVzRqcC9wcS9rNmJ2SGI3YU80WTRLSWJDUDJjd2tjTGNtU1RMTVViS3RoZHF6MmN1TG0wV3dFRHJqb2VOQ0p2RzlkQTdNTy9oSDk4VlN6alE0TFFxN3p3OVVKVHNLS2NOK0J5bDB0QS9hZ1Z2TCt4WmUwcDZZaU94ZzVkcWN0S0gyam1PR1IwZzkreFdXTjViOXMrYzQ4UHFYMDZnUnBwbnh3YUVjNTZwcXYzTkk5TE5PWnBOd3RoK2llaGpiRUg3ckR4MG1XUFFBaCtUQlU4V2o4TitOcXZVM01PSXB6V242MTFCQ0VBb3JrdFI4SWtGbEl3SGtqb2ZZbklqVWpCdHdNN3J0VDc5cm1Ud1RMSWxWZDFUSUtYN1hYdUNqTzNqNDhQbTlmdTJSR2gwK3F6VnEzNStaWVlvYllSc29jMjAvZzNlcGdUVFgzaDlnbGdzZWxRMWR5N0VVWEpjaVBxZ0d3Mk4yNmc0U0IrR25aUzdmTnBqYURySTdtK3dTSjEiLCJtYWMiOiJhN2E0Yzg4ZjY1ZGI0ODNkYzg1ZDYxMjdlNTY3MjU2NTg0YTc5OWNhZDMxNjYxMzVhNjUzMjFhMDNmNjI3ZTljIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:34:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikt4WFd5WUN6bUxtUXRycEtnaE5oemc9PSIsInZhbHVlIjoiOVhjcTdsdEJnQkV5MW5va0U4VHZVYnBnY25TQTdqellGdC96QU5FWWhXQlVJeW1OL2Q3UmVMNHVCWHhGVWFwREw2TndxaWV6ZHNXV0FJakpLMFc2bUZ1cWRTckJMUzI2S1A4dWZubFpIWjFTbHZ3NDBxT1BlbGJQSHZ6ZnYyejA5cHpqNkU5MUZNNG1KVHY4Qzl6dVBpVWFtNjRreXVHaktwcTJHREhhZUk2aWJ2SnhpRkJ0NTR2K3U4UkxoRk5adjhEakNOOXpzZElteHlzd0NWdHAwbjZNV0NtVU56SE1CdWNwTlhsamlSc0tuWm1GNC9QSWwweTJSU1M0M0hSZmM1ZXJyd0o2QXBySzNmUm9OdDJsM2J1d2pZV0szYnQ3ZUxYRWttTzhiMVY3L3JocmE3b1lXRHdpVEdySm1oS2JSME95TkRRcnhBcHByQVB0WllyK0U2dGE5ZXlWd0ZveWpkSnF0Wk96Vlh5Rk5FVVg0UW0xVlcvNTVDTzFDUDF2RTVZT2d3TkdXT3QxQzlvOWJQNkxrbmlCeDhmU1Ria2pHU3llSkorNmVyWFNGYU5qd1h6dW5PcWpBVDdQREJwWTB5NXdQUnpQV2tXaHgyZHNPVVVRUVY4UGxET2tnQnQzZm1xZDNRUE44WkpySDF3MVZxalkxY1VGZHd1cGlWODMiLCJtYWMiOiIyY2NkYTA0YjI1OTBiM2I1MGUxMmE5ODYyZDFiMzM0ZThjMjQ0OGZlOGY3YjhmZjAwNGMxOWU1NWE0ZGM0ZDFlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:34:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1912266346\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1999648684 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mcfCnapUuTHYoKdSxiJAUL0QamaqkhFflrxQ9Mt9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999648684\", {\"maxDepth\":0})</script>\n"}}
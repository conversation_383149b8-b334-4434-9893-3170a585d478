{"__meta": {"id": "Xb2de9dda1660207968dccd08a16a8075", "datetime": "2025-07-29 17:37:23", "utime": **********.409325, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753810642.103727, "end": **********.409369, "duration": 1.3056418895721436, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1753810642.103727, "relative_start": 0, "end": **********.322299, "relative_end": **********.322299, "duration": 1.2185719013214111, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.322337, "relative_start": 1.****************, "end": **********.409374, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "87.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3023\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1843 to 1849\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1843\" onclick=\"\">routes/web.php:1843-1849</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0LomX1r5AcX0BuqBuBd7VPfs3InQ8dHZp0K2HyJp", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1047908209 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1047908209\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-333460921 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-333460921\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-115133880 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-115133880\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-384552265 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384552265\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-643567220 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-643567220\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2098426009 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 17:37:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iit4ZHZnS29jL1k1amhZTm15TlJhRXc9PSIsInZhbHVlIjoiR2xBVW1uWWJicUwxRUtuVEhRSW5YVmNIS3RicDVsR2dORkR2NGVQM1FEYVBMRjVMazVTdUZGVTVibFZPc3FPNUNuaWxlcHdGYUQ4dzJ6TXRQV3R5WThZY0NTZks3dmxHRFpIZXB4bmo0QmJBVTlzVjZidDdoTHBFWnR5cEt2UnBpWTNySW1KakltUDBSSEx2YVJFeVkxeTR0NEFqOGpvNitzdjNDcVdKbnRmNW5SNUpwQjNBaTErYXd3T2ptNGx3YjRTNjhDN2J0dzhVOCtWb3NDU09WODZ3eWRRUFpPZGZsUUpsS00zWG42bTR2TnA5Y0drR0lrRGh6WVFnZGZMUmRsVDNKb1BFcEtNR3JKR0phdlBHbDJpVXh4UUdSeVhwYWVNTlNvUVZ2MFlOaTg0bldBb3ZQbWtaMU0xd241VS9nKzRLOTZWZnJ0M1puK0FueU9FZTF0MXJaV1ZhVE10N2Y5aWNQeXZPV0grNndJVi9UQm1PaGZ1U0VDTVZjOXpQMVhkNWtodHpyZEE0R2YvaGNyN0lpamRMMEdlSS9wdHBCMEhBSXczclcxalE2bFZHNHZ0VTdmUGVicHUrMDAxeDFoQTNSLzB2Y3VabmNjeHZQekNMUitvWURsNnh1YnZObUVvR3R4L3ZERkRLL1Zlb2ZVUmc1VGxkelZGd2V0WWYiLCJtYWMiOiJjZmQ1OTYxNTExN2FjMjVhMWIyMDFjNDQ1MDkwNGQ5NmJhNzg1NjU4NGE0NDIyNjQ2MzY3MGU2NjE5ZTE3Y2ViIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:37:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjRiMnpFT1AxS2Q3WDFPandGeFFtUEE9PSIsInZhbHVlIjoid21NZ0MzeGlOaUFPVjNuZ21OekJPTng1dmE4aXc3VlIyV0UwcDdkenRIRjd2dDdqeDdzRWtJY0E3bFJYa0lIdERUN2RrMytvV2hQMHhXQnJBTWwxNFhBQ21IYllZbTU1dDVBb0lkd0VjNFd1dGtSeE04NGNlSHlvWUdIalVEY3FYclcrTnNGZDVFcHpsQ3dxNmpjZzdDZzZmbnFOb3pwRlcveEZOdEZqUVN4dmttNUtKMFROa0pRMXRrU0g0WE9PZVN1azFPMVhET2xnakplMFpvOXJwd0VpSnhpUUFDLzUxVHdBc21LeFhxaXU4S0FCMG50bkgrNFBIeks5RHF2RE43MDkvSG9jZ0RFQkdJUWFhL1RIazNTay8yR2xMbllLV2lwc3psaUNVNjRMY2ZpK0IrTHdsMVpML2pvcUxWUnArUTJUYXJ0TlI5alJmNmQ0bHkrNEtiZ2RqbmlxdTZMalhRRlN2ZVZPWEx3QkNpdEQyRGorSzByL2dKd2FIaWlqZURoQUNyY0g1VGhqcnFvTTBpTnFsYnluNDNxaU44RCsyQmFYNmo1NlJVdUZka3JUbXBjcTEveC9tekpMT3FXUXo2REEyN2lrMUxDd3JTRTBFYkNVM0daSHh4OXVmcDgrR3RSZHRQQUZPTkh2Y3ZKcHQ4QnBNdUV4YTYzMVZ1c0QiLCJtYWMiOiI5ZmQwODNkZWY0M2M0ZjdkNDg1NzZmZGU2NTQ5NDcyOGEyMzZmMWQ0ZTM2YzI4MzJkY2U4OTA0N2I1N2E4NmEyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 19:37:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iit4ZHZnS29jL1k1amhZTm15TlJhRXc9PSIsInZhbHVlIjoiR2xBVW1uWWJicUwxRUtuVEhRSW5YVmNIS3RicDVsR2dORkR2NGVQM1FEYVBMRjVMazVTdUZGVTVibFZPc3FPNUNuaWxlcHdGYUQ4dzJ6TXRQV3R5WThZY0NTZks3dmxHRFpIZXB4bmo0QmJBVTlzVjZidDdoTHBFWnR5cEt2UnBpWTNySW1KakltUDBSSEx2YVJFeVkxeTR0NEFqOGpvNitzdjNDcVdKbnRmNW5SNUpwQjNBaTErYXd3T2ptNGx3YjRTNjhDN2J0dzhVOCtWb3NDU09WODZ3eWRRUFpPZGZsUUpsS00zWG42bTR2TnA5Y0drR0lrRGh6WVFnZGZMUmRsVDNKb1BFcEtNR3JKR0phdlBHbDJpVXh4UUdSeVhwYWVNTlNvUVZ2MFlOaTg0bldBb3ZQbWtaMU0xd241VS9nKzRLOTZWZnJ0M1puK0FueU9FZTF0MXJaV1ZhVE10N2Y5aWNQeXZPV0grNndJVi9UQm1PaGZ1U0VDTVZjOXpQMVhkNWtodHpyZEE0R2YvaGNyN0lpamRMMEdlSS9wdHBCMEhBSXczclcxalE2bFZHNHZ0VTdmUGVicHUrMDAxeDFoQTNSLzB2Y3VabmNjeHZQekNMUitvWURsNnh1YnZObUVvR3R4L3ZERkRLL1Zlb2ZVUmc1VGxkelZGd2V0WWYiLCJtYWMiOiJjZmQ1OTYxNTExN2FjMjVhMWIyMDFjNDQ1MDkwNGQ5NmJhNzg1NjU4NGE0NDIyNjQ2MzY3MGU2NjE5ZTE3Y2ViIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:37:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjRiMnpFT1AxS2Q3WDFPandGeFFtUEE9PSIsInZhbHVlIjoid21NZ0MzeGlOaUFPVjNuZ21OekJPTng1dmE4aXc3VlIyV0UwcDdkenRIRjd2dDdqeDdzRWtJY0E3bFJYa0lIdERUN2RrMytvV2hQMHhXQnJBTWwxNFhBQ21IYllZbTU1dDVBb0lkd0VjNFd1dGtSeE04NGNlSHlvWUdIalVEY3FYclcrTnNGZDVFcHpsQ3dxNmpjZzdDZzZmbnFOb3pwRlcveEZOdEZqUVN4dmttNUtKMFROa0pRMXRrU0g0WE9PZVN1azFPMVhET2xnakplMFpvOXJwd0VpSnhpUUFDLzUxVHdBc21LeFhxaXU4S0FCMG50bkgrNFBIeks5RHF2RE43MDkvSG9jZ0RFQkdJUWFhL1RIazNTay8yR2xMbllLV2lwc3psaUNVNjRMY2ZpK0IrTHdsMVpML2pvcUxWUnArUTJUYXJ0TlI5alJmNmQ0bHkrNEtiZ2RqbmlxdTZMalhRRlN2ZVZPWEx3QkNpdEQyRGorSzByL2dKd2FIaWlqZURoQUNyY0g1VGhqcnFvTTBpTnFsYnluNDNxaU44RCsyQmFYNmo1NlJVdUZka3JUbXBjcTEveC9tekpMT3FXUXo2REEyN2lrMUxDd3JTRTBFYkNVM0daSHh4OXVmcDgrR3RSZHRQQUZPTkh2Y3ZKcHQ4QnBNdUV4YTYzMVZ1c0QiLCJtYWMiOiI5ZmQwODNkZWY0M2M0ZjdkNDg1NzZmZGU2NTQ5NDcyOGEyMzZmMWQ0ZTM2YzI4MzJkY2U4OTA0N2I1N2E4NmEyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 19:37:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2098426009\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-530836830 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0LomX1r5AcX0BuqBuBd7VPfs3InQ8dHZp0K2HyJp</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-530836830\", {\"maxDepth\":0})</script>\n"}}